# Design Document

## Overview

This design document outlines the comprehensive enhancement of the legal scheduling system's web interface. The enhancement focuses on reorganizing the tab layout, improving the intelligent scheduling functionality, standardizing the four main tabs framework, implementing consistent pagination, and fixing existing issues. The design ensures better user experience through improved layout, consistent styling, enhanced functionality, and proper error handling.

## Architecture

### Current System Architecture
- **Frontend**: HTML templates with Bootstrap 5.1.3 and Font Awesome 6.0.0
- **Backend**: Go with Gin framework serving REST APIs
- **Database**: GORM with relational database
- **Styling**: CSS with custom styles for genetic algorithm visualization

### Enhanced Architecture Components
1. **Tab Management System**: Reorganized tab structure with intelligent scheduling as primary tab
2. **State Management**: Cookie-based persistence for user preferences
3. **Pagination System**: Unified pagination controls across all tabs
4. **Time Selection Component**: Date range picker for intelligent scheduling
5. **Status Filter System**: Radio button-based status filtering
6. **Error Handling**: Comprehensive 404 error resolution

## Components and Interfaces

### 1. Tab Navigation Component
```html
<!-- Enhanced tab structure -->
<ul class="nav nav-tabs" id="mainTabs">
  <!-- Primary tab: Intelligent Scheduling (left-aligned) -->
  <li class="nav-item">
    <button class="nav-link active" id="genetic-tab">智能调度</button>
  </li>
  
  <!-- Secondary tabs: Right-aligned with ms-auto -->
  <li class="nav-item ms-auto">
    <button class="nav-link" id="lawyers-tab">律师</button>
  </li>
  <!-- Additional tabs... -->
</ul>
```

**Key Features:**
- Intelligent scheduling tab positioned first on the left
- Remaining 4 tabs maintain current order, right-aligned using Bootstrap's `ms-auto` class
- Consistent tab styling and behavior

### 2. Time Selection Component
```html
<!-- Time range selector for intelligent scheduling -->
<div class="row mb-3">
  <div class="col-md-6">
    <label for="startDate" class="form-label">开始日期</label>
    <input type="date" class="form-control" id="startDate">
  </div>
  <div class="col-md-6">
    <label for="endDate" class="form-label">结束日期</label>
    <input type="date" class="form-control" id="endDate">
  </div>
</div>
```

**Functionality:**
- Default range: Two weeks starting from tomorrow
- Selectable range: Two weeks from tomorrow
- Integration with database filtering for court sessions
- Cookie persistence for user preferences

### 3. Status Filter System
```html
<!-- Radio button status filters -->
<div class="btn-group" role="group" aria-label="状态筛选">
  <input type="radio" class="btn-check" name="statusRadio" id="statusAll" value="" checked>
  <label class="btn btn-outline-secondary" for="statusAll">
    <i class="fas fa-list"></i> 全部状态
  </label>
  <!-- Additional status options... -->
</div>
```

**Features:**
- Single selection using radio buttons
- Consistent icons matching table status indicators
- State persistence via cookies

### 4. Enhanced Pagination Component
```html
<!-- Unified pagination controls -->
<div class="pagination-container">
  <div class="pagination-info">
    显示 <select id="pageSizeSelect">
      <option value="20">20</option>
      <option value="50" selected>50</option>
      <option value="100">100</option>
      <option value="200">200</option>
    </select> 条记录
  </div>
  
  <nav aria-label="分页导航">
    <ul class="pagination">
      <!-- Enhanced pagination with more page numbers and ellipsis -->
    </ul>
  </nav>
</div>
```

**Enhancements:**
- Default 50 records per page
- Page size options: 20, 50, 100, 200
- Extended page number display with ellipsis for large datasets
- Cookie-based preference storage

### 5. Lawyer Address Display Enhancement
```css
.lawyer-name {
  font-weight: bold;
  font-size: 14px; /* Reduced from larger size to match court styling */
  color: #1976d2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

**Changes:**
- Reduced font size to match court address styling
- Maintained readability and consistency

## Data Models

### Cookie Storage Schema
```javascript
// User preferences stored in cookies
const userPreferences = {
  pageSize: 50,                    // Selected page size
  lawyerStatus: '',                // Selected lawyer status filter
  courtStatus: '',                 // Selected court status filter
  trialStatus: '',                 // Selected trial status filter
  routeStatus: '',                 // Selected route status filter
  timeRange: {                     // Intelligent scheduling time range
    startDate: 'YYYY-MM-DD',
    endDate: 'YYYY-MM-DD'
  }
};
```

### API Request/Response Enhancements
```javascript
// Enhanced pagination response
{
  "data": [...],
  "total": 1000,
  "page": 1,
  "page_size": 50,
  "total_pages": 20,
  "has_next": true,
  "has_prev": false
}

// Time-filtered genetic scheduling request
{
  "configs": [...],
  "start_date": "2024-01-15",
  "end_date": "2024-01-29"
}
```

## Error Handling

### 404 Error Resolution Strategy
1. **Route Audit**: Comprehensive review of all frontend route references
2. **Static Asset Verification**: Ensure all CSS, JS, and image references are correct
3. **API Endpoint Validation**: Verify all AJAX calls use correct endpoints
4. **Template Path Correction**: Fix any incorrect template or partial references

### Implementation Steps
```go
// Enhanced error handling in web service
func (w *WebService) handleNotFound(c *gin.Context) {
    c.JSON(http.StatusNotFound, gin.H{
        "error": "资源未找到",
        "path": c.Request.URL.Path,
        "method": c.Request.Method,
    })
}

// Add 404 handler to router
r.NoRoute(w.handleNotFound)
```

### Error Logging
- Implement comprehensive error logging for debugging
- Add request tracking for 404 errors
- Provide meaningful error messages to users

## Testing Strategy

### Frontend Testing
1. **Cross-browser Compatibility**: Test tab navigation and styling across major browsers
2. **Responsive Design**: Verify layout works on different screen sizes
3. **Cookie Functionality**: Test preference persistence across sessions
4. **Time Selection**: Validate date picker functionality and constraints

### Backend Testing
1. **API Endpoint Testing**: Verify all routes return correct responses
2. **Database Query Testing**: Test time-filtered queries for genetic scheduling
3. **Error Handling Testing**: Verify 404 and other error responses
4. **Performance Testing**: Ensure pagination performs well with large datasets

### Integration Testing
1. **Tab Switching**: Test seamless navigation between tabs
2. **Filter Integration**: Verify status filters work with pagination
3. **Time Range Integration**: Test genetic scheduling with date filters
4. **Cookie Integration**: Verify preferences persist across all components

### User Acceptance Testing
1. **Layout Verification**: Confirm tab positioning meets requirements
2. **Font Consistency**: Verify lawyer address font matches court styling
3. **Pagination Usability**: Test enhanced pagination controls
4. **Time Selection Usability**: Verify intuitive date range selection

## Implementation Phases

### Phase 1: Tab Layout Reorganization
- Reorder tabs with intelligent scheduling first
- Implement right-alignment for secondary tabs
- Update CSS for consistent styling

### Phase 2: Status Filter Enhancement
- Convert existing filters to radio button style
- Implement consistent icons
- Add cookie persistence

### Phase 3: Pagination System Upgrade
- Implement unified pagination component
- Add page size selection
- Enhance page number display with ellipsis

### Phase 4: Time Selection Integration
- Add date picker to intelligent scheduling tab
- Implement default two-week range
- Integrate with backend filtering

### Phase 5: Styling Consistency
- Adjust lawyer address font size
- Ensure consistent button alignment
- Fix responsive layout issues

### Phase 6: Error Resolution
- Audit and fix 404 errors
- Implement comprehensive error handling
- Add error logging and monitoring

## Performance Considerations

### Frontend Optimization
- Minimize DOM manipulation during tab switching
- Implement efficient cookie management
- Optimize CSS for better rendering performance

### Backend Optimization
- Implement efficient database queries for time-filtered data
- Add appropriate database indexes for pagination
- Optimize API response sizes

### Caching Strategy
- Implement browser caching for static assets
- Consider server-side caching for frequently accessed data
- Use efficient cookie storage patterns

## Security Considerations

### Cookie Security
- Implement secure cookie settings
- Validate cookie data before use
- Set appropriate expiration times

### Input Validation
- Validate date range inputs
- Sanitize search parameters
- Implement proper error handling for invalid inputs

### API Security
- Maintain existing authentication mechanisms
- Validate all API parameters
- Implement rate limiting if needed