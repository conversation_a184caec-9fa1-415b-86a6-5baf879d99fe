package schedule

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// 全局随机数种子管理
var (
	globalSeed int64 = time.Now().UnixNano()
	seedMutex  sync.Mutex
)

// GAWeightConfig 权重配置结构
type GAWeightConfig struct {
	Name          string  // 配置名称
	DistWeight    float64 // 距离权重(每公里的费用)
	CostWeight    float64 // 费用权重
	TimeWeight    float64 // 时间权重(每分钟的费用)
	BalanceWeight float64 // 负载均衡权重
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	ExecutionTime    time.Duration
	GenerationsRun   int
	BestFitness      float64
	ConvergenceGen   int
	TotalSolutions   int
	ValidSolutions   int
}

// InternalState 内部状态（用于测试）
type InternalState struct {
	LawyersCount    int
	CourtsCount     int
	TrialsCount     int
	RoutesCount     int
	ConflictsMatrix bool
	CostMatrix      bool
}

// CostResult 费用计算结果
type CostResult struct {
	Cost  float64
	Valid bool
	Error error
}

// validateConfig 验证遗传算法配置
func (s *GeneticAlgorithmScheduler) validateConfig() error {
	if s.populationSize <= 0 {
		return fmt.Errorf("种群大小必须为正数，当前值: %d", s.populationSize)
	}
	if s.generations <= 0 {
		return fmt.Errorf("进化代数必须为正数，当前值: %d", s.generations)
	}
	if s.mutationRate < 0 || s.mutationRate > 1 {
		return fmt.Errorf("变异率必须在0-1之间，当前值: %.2f", s.mutationRate)
	}
	if s.crossoverRate < 0 || s.crossoverRate > 1 {
		return fmt.Errorf("交叉率必须在0-1之间，当前值: %.2f", s.crossoverRate)
	}
	if s.maxCapacityPerLawyer <= 0 {
		return fmt.Errorf("律师最大容量必须为正数，当前值: %d", s.maxCapacityPerLawyer)
	}
	return nil
}

// SetRandomSeed 设置随机数种子（用于测试）
func (s *GeneticAlgorithmScheduler) SetRandomSeed(seed int64) {
	seedMutex.Lock()
	defer seedMutex.Unlock()
	globalSeed = seed
	rand.Seed(seed)
}

// SetGAParameters 设置遗传算法参数
func (s *GeneticAlgorithmScheduler) SetGAParameters(populationSize, generations int, mutationRate, crossoverRate float64) error {
	if populationSize <= 0 {
		return fmt.Errorf("种群大小必须为正数")
	}
	if generations <= 0 {
		return fmt.Errorf("进化代数必须为正数")
	}
	if mutationRate < 0 || mutationRate > 1 {
		return fmt.Errorf("变异率必须在0-1之间")
	}
	if crossoverRate < 0 || crossoverRate > 1 {
		return fmt.Errorf("交叉率必须在0-1之间")
	}
	
	s.populationSize = populationSize
	s.generations = generations
	s.mutationRate = mutationRate
	s.crossoverRate = crossoverRate
	
	return nil
}

// GetInternalState 获取内部状态（用于测试）
func (s *GeneticAlgorithmScheduler) GetInternalState() InternalState {
	return InternalState{
		LawyersCount:    len(s.lawyerMap),
		CourtsCount:     len(s.courtMap),
		TrialsCount:     len(s.trialMap),
		RoutesCount:     len(s.routeMap),
		ConflictsMatrix: s.conflicts != nil,
		CostMatrix:      s.currentCosts != nil,
	}
}

// GetMemoryUsage 获取内存使用情况
func (s *GeneticAlgorithmScheduler) GetMemoryUsage() map[string]interface{} {
	state := s.GetInternalState()
	
	// 估算各种数据结构的内存使用
	conflictMapSize := len(s.conflicts)
	costMatrixSize := 0
	if s.currentCosts != nil && len(s.currentCosts) > 0 {
		costMatrixSize = len(s.currentCosts) * len(s.currentCosts[0])
	}
	
	return map[string]interface{}{
		"lawyers_count":       state.LawyersCount,
		"courts_count":        state.CourtsCount,
		"trials_count":        state.TrialsCount,
		"routes_count":        state.RoutesCount,
		"conflict_map_size":   conflictMapSize,
		"cost_matrix_size":    costMatrixSize,
		"estimated_memory_mb": (conflictMapSize*50 + costMatrixSize*8) / (1024 * 1024), // 粗略估算
	}
}

// OptimizeForLargeDataset 为大数据集优化参数
func (s *GeneticAlgorithmScheduler) OptimizeForLargeDataset() {
	state := s.GetInternalState()
	
	// 根据数据集大小调整参数
	if state.LawyersCount > 100 || state.TrialsCount > 1000 {
		// 大数据集：减少种群大小和代数
		s.populationSize = 50
		s.generations = 30
		s.mutationRate = 0.15
		s.crossoverRate = 0.8
		fmt.Printf("检测到大数据集，已优化GA参数：种群=%d，代数=%d\n", s.populationSize, s.generations)
	} else if state.LawyersCount < 20 && state.TrialsCount < 100 {
		// 小数据集：可以使用更大的参数
		s.populationSize = 100
		s.generations = 50
		s.mutationRate = 0.1
		s.crossoverRate = 0.7
		fmt.Printf("检测到小数据集，已优化GA参数：种群=%d，代数=%d\n", s.populationSize, s.generations)
	}
}

// ValidateDataIntegrity 验证数据完整性
func (s *GeneticAlgorithmScheduler) ValidateDataIntegrity() []string {
	errors := make([]string, 0)
	
	// 检查基本数据
	if len(s.lawyerMap) == 0 {
		errors = append(errors, "律师数据为空")
	}
	if len(s.courtMap) == 0 {
		errors = append(errors, "法院数据为空")
	}
	if len(s.trialMap) == 0 {
		errors = append(errors, "案件数据为空")
	}
	if len(s.routeMap) == 0 {
		errors = append(errors, "路径数据为空")
	}
	
	// 检查案件-法院关联
	for trialID, trial := range s.trialMap {
		if _, exists := s.courtMap[trial.CourtID]; !exists {
			errors = append(errors, fmt.Sprintf("案件 %d 关联的法院 %d 不存在", trialID, trial.CourtID))
		}
	}
	
	// 检查路径覆盖率
	missingRoutes := 0
	totalRoutes := 0
	for _, lawyer := range s.lawyerMap {
		for _, court := range s.courtMap {
			totalRoutes++
			routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
			if _, exists := s.routeMap[routeKey]; !exists {
				missingRoutes++
			}
		}
	}
	
	if missingRoutes > 0 {
		coverage := float64(totalRoutes-missingRoutes) / float64(totalRoutes) * 100
		errors = append(errors, fmt.Sprintf("路径覆盖率: %.1f%% (%d/%d)", coverage, totalRoutes-missingRoutes, totalRoutes))
	}
	
	return errors
}