package schedule

import (
	"fmt"
	"sort"
	"time"
)

// validateNoTimeConflicts 验证方案是否没有时间冲突
func (s *GeneticAlgorithmScheduler) validateNoTimeConflicts(matching map[uint]uint) bool {
	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的案件是否有时间冲突
	for lawyerID, trials := range lawyerTrials {
		if len(trials) <= 1 {
			continue // 单个案件不会冲突
		}

		// 检查该律师的所有案件对
		for i := 0; i < len(trials); i++ {
			for j := i + 1; j < len(trials); j++ {
				if s.hasTimeConflict(lawyerID, trials[i], trials[j]) {
					return false
				}
			}
		}
	}

	return true
}

// hasTimeConflict 检查两个案件是否有时间冲突
func (s *GeneticAlgorithmScheduler) hasTimeConflict(lawyerID, trialID1, trialID2 uint) bool {
	trial1 := s.trialMap[trialID1]
	trial2 := s.trialMap[trialID2]

	if trial1 == nil || trial2 == nil {
		return true // 数据缺失，认为冲突
	}

	// 计算第一个案件的时间窗口
	start1, end1, err1 := s.calculateTravelTime(lawyerID, trial1)
	if err1 != nil {
		return true // 无法计算时间，认为冲突
	}

	// 计算第二个案件的时间窗口
	start2, end2, err2 := s.calculateTravelTime(lawyerID, trial2)
	if err2 != nil {
		return true // 无法计算时间，认为冲突
	}

	// 检查是否重叠
	return s.isTimeOverlap(start1, end1, start2, end2)
}

// validateSingleLawyerSchedule 验证单个律师的时间安排
func (s *GeneticAlgorithmScheduler) validateSingleLawyerSchedule(lawyerID uint, trials []uint) (bool, []string) {
	if len(trials) <= 1 {
		return true, []string{} // 单个案件不会冲突
	}

	errors := make([]string, 0)

	// 获取所有案件的时间窗口
	type timeWindow struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
	}

	windows := make([]timeWindow, 0, len(trials))
	for _, trialID := range trials {
		trial := s.trialMap[trialID]
		if trial == nil {
			errors = append(errors, fmt.Sprintf("案件 %d 数据缺失", trialID))
			continue
		}

		start, end, err := s.calculateTravelTime(lawyerID, trial)
		if err != nil {
			errors = append(errors, fmt.Sprintf("案件 %d 无法计算出行时间: %v", trialID, err))
			continue
		}

		windows = append(windows, timeWindow{
			trialID:   trialID,
			startTime: start,
			endTime:   end,
		})
	}

	// 检查所有时间窗口是否有重叠
	for i := 0; i < len(windows); i++ {
		for j := i + 1; j < len(windows); j++ {
			if s.isTimeOverlap(windows[i].startTime, windows[i].endTime,
				windows[j].startTime, windows[j].endTime) {
				errors = append(errors, fmt.Sprintf("案件 %d 和案件 %d 时间冲突：[%s-%s] vs [%s-%s]",
					windows[i].trialID, windows[j].trialID,
					windows[i].startTime.Format("15:04"), windows[i].endTime.Format("15:04"),
					windows[j].startTime.Format("15:04"), windows[j].endTime.Format("15:04")))
			}
		}
	}

	return len(errors) == 0, errors
}

// enforceNoTimeConflicts 强制执行无时间冲突
func (s *GeneticAlgorithmScheduler) enforceNoTimeConflicts(matching map[uint]uint) map[uint]uint {
	if len(matching) == 0 {
		return matching
	}

	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	validMatching := make(map[uint]uint)

	// 为每个律师解决时间冲突
	for lawyerID, trials := range lawyerTrials {
		nonConflictingTrials := s.selectNonConflictingTrials(lawyerID, trials)
		for _, trialID := range nonConflictingTrials {
			validMatching[trialID] = lawyerID
		}
	}

	return validMatching
}

// selectNonConflictingTrials 选择无时间冲突的案件
func (s *GeneticAlgorithmScheduler) selectNonConflictingTrials(lawyerID uint, trials []uint) []uint {
	if len(trials) <= 1 {
		return trials
	}

	// 获取所有案件的时间和费用信息
	type trialInfo struct {
		trialID   uint
		startTime time.Time
		endTime   time.Time
		cost      float64
	}

	timeInfos := make([]trialInfo, 0, len(trials))
	for _, trialID := range trials {
		trial := s.trialMap[trialID]
		if trial == nil {
			continue
		}

		startTime, endTime, err := s.calculateTravelTime(lawyerID, trial)
		if err != nil {
			continue
		}

		cost := s.calculateAssignmentCost(trialID, lawyerID)
		timeInfos = append(timeInfos, trialInfo{
			trialID:   trialID,
			startTime: startTime,
			endTime:   endTime,
			cost:      cost,
		})
	}

	// 按费用排序（优先选择费用低的）
	sort.Slice(timeInfos, func(i, j int) bool {
		return timeInfos[i].cost < timeInfos[j].cost
	})

	// 贪心选择无冲突的案件
	selected := make([]uint, 0)
	selectedWindows := make([]trialInfo, 0)

	for _, info := range timeInfos {
		hasConflict := false
		for _, selectedInfo := range selectedWindows {
			if s.isTimeOverlap(info.startTime, info.endTime, selectedInfo.startTime, selectedInfo.endTime) {
				hasConflict = true
				break
			}
		}

		if !hasConflict {
			selected = append(selected, info.trialID)
			selectedWindows = append(selectedWindows, info)
		}
	}

	return selected
}

// performDetailedValidation 执行详细的时间冲突验证
func (s *GeneticAlgorithmScheduler) performDetailedValidation(matching map[uint]uint) (bool, []string) {
	errors := make([]string, 0)

	// 按律师分组
	lawyerTrials := make(map[uint][]uint)
	for trialID, lawyerID := range matching {
		lawyerTrials[lawyerID] = append(lawyerTrials[lawyerID], trialID)
	}

	// 检查每个律师的时间安排
	for lawyerID, trials := range lawyerTrials {
		lawyer := s.lawyerMap[lawyerID]
		if lawyer == nil {
			errors = append(errors, fmt.Sprintf("律师 %d 数据缺失", lawyerID))
			continue
		}

		isValid, lawyerErrors := s.validateSingleLawyerSchedule(lawyerID, trials)
		if !isValid {
			errors = append(errors, fmt.Sprintf("律师 %s (ID:%d) 存在时间冲突:", lawyer.Name, lawyerID))
			for _, err := range lawyerErrors {
				errors = append(errors, fmt.Sprintf("  - %s", err))
			}
		}
	}

	return len(errors) == 0, errors
}