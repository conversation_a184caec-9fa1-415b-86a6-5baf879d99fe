package schedule

import (
	"math"
)

// evaluateIndividual 评估个体适应度
func (s *GeneticAlgorithmScheduler) evaluateIndividual(individual *Individual) {
	numTrials := len(individual.Genes)
	numLawyers := len(s.lawyersList)

	// 按律师分组
	lawyerTrials := make([][]int, numLawyers)
	totalCost := 0.0
	penalty := 0.0

	// 计算基本费用和匹配数
	for t := 0; t < numTrials; t++ {
		l := individual.Genes[t]
		if l >= 0 && l < numLawyers {
			lawyerTrials[l] = append(lawyerTrials[l], t)
			cost := s.currentCosts[t][l]
			if math.IsInf(cost, 1) {
				penalty += 1e12
			} else {
				totalCost += cost
			}
		}
	}

	const conflictPenalty = 1e12
	const capacityPenalty = 1e9
	const matchingBonus = 1e6

	// 检查约束违反
	for l := 0; l < numLawyers; l++ {
		trials := lawyerTrials[l]
		numAssigned := len(trials)

		// 容量约束
		if numAssigned > s.maxCapacityPerLawyer {
			penalty += float64(numAssigned-s.maxCapacityPerLawyer) * capacityPenalty
		}

		// 时间冲突约束 - 使用更严格的检查
		if numAssigned > 1 {
			// 检查预计算的冲突矩阵
			numConflicts := 0
			lawyerID := s.lawyersList[l]
			for i := 0; i < numAssigned; i++ {
				for j := i + 1; j < numAssigned; j++ {
					key := s.getConflictKey(lawyerID, trials[i], trials[j])
					if s.conflicts[key] {
						numConflicts++
					}
				}
			}

			// 如果预计算冲突矩阵检测到冲突，施加惩罚
			if numConflicts > 0 {
				penalty += float64(numConflicts) * conflictPenalty
			}

			// 额外的实时冲突检查（双重保险）
			actualTrialIDs := make([]uint, 0, numAssigned)
			for _, trialIdx := range trials {
				actualTrialIDs = append(actualTrialIDs, s.trialsList[trialIdx])
			}

			// 使用实际的时间冲突检查
			for i := 0; i < len(actualTrialIDs); i++ {
				for j := i + 1; j < len(actualTrialIDs); j++ {
					if s.hasTimeConflict(lawyerID, actualTrialIDs[i], actualTrialIDs[j]) {
						penalty += conflictPenalty
					}
				}
			}
		}
	}

	// 如果有惩罚，适应度为惩罚值
	if penalty > 0 {
		individual.Fitness = penalty + totalCost
		return
	}

	// 计算匹配数
	matching := 0
	for l := 0; l < numLawyers; l++ {
		matching += len(lawyerTrials[l])
	}

	// 计算负载均衡费用
	loads := make([]float64, numLawyers)
	for l := 0; l < numLawyers; l++ {
		loads[l] = float64(len(lawyerTrials[l]))
	}
	mean := float64(matching) / float64(numLawyers)
	variance := 0.0
	for _, load := range loads {
		variance += math.Pow(load-mean, 2)
	}
	variance /= float64(numLawyers)
	balanceCost := variance * s.currentBalanceWeight
	totalCost += balanceCost

	// 适应度：最小化，所以是负的匹配奖励加上总费用
	individual.Fitness = -float64(matching)*matchingBonus + totalCost
}

// calculateMetrics 计算方案指标
func (s *GeneticAlgorithmScheduler) calculateMetrics(matching map[uint]uint) (int, int) {
	totalDistance := 0
	totalDuration := 0

	for trialID, lawyerID := range matching {
		trial := s.trialMap[trialID]
		lawyer := s.lawyerMap[lawyerID]
		court := s.courtMap[trial.CourtID]

		if trial == nil || lawyer == nil || court == nil {
			continue
		}

		routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
		route, exists := s.routeMap[routeKey]
		if !exists || route.Distance == 0 {
			continue
		}

		// 往返距离和时间
		totalDistance += route.Distance * 2
		totalDuration += route.Duration * 2

		// 庭审时间
		trialDuration := int(trial.EndTime.Sub(trial.StartTime).Minutes())
		totalDuration += trialDuration
	}

	return totalDistance, totalDuration
}

// calculateActualCost 计算真实的总费用
func (s *GeneticAlgorithmScheduler) calculateActualCost(matching map[uint]uint) float64 {
	totalCost := 0.0

	for trialID, lawyerID := range matching {
		// 直接使用 calculateAssignmentCost 计算费用
		cost := s.calculateAssignmentCost(trialID, lawyerID)
		if !math.IsInf(cost, 1) {
			totalCost += cost
		}
	}

	// 计算负载均衡费用
	lawyerLoads := make(map[uint]int)
	for _, lawyerID := range matching {
		lawyerLoads[lawyerID]++
	}

	// 计算负载方差
	totalMatches := len(matching)
	numLawyers := len(s.lawyersList)
	meanLoad := float64(totalMatches) / float64(numLawyers)

	variance := 0.0
	for _, lawyerID := range s.lawyersList {
		load := float64(lawyerLoads[lawyerID])
		variance += math.Pow(load-meanLoad, 2)
	}
	variance /= float64(numLawyers)

	balanceCost := variance * s.currentBalanceWeight
	totalCost += balanceCost

	return totalCost
}