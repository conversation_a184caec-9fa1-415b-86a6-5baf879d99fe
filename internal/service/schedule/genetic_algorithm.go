package schedule

import (
	"external-tool/internal/model"
	"external-tool/internal/model/imap"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"time"
)

// 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixNano())
}

// BestSchedule 调度器接口
type BestSchedule interface {
	Schedule() map[uint][]map[uint]uint
}

// 常量定义
const (
	Buffer               = 1.5
	DistanceNotAvailable = -1
)

// GeneticAlgorithmScheduler 遗传算法调度器
type GeneticAlgorithmScheduler struct {
	mapModel             imap.MapModel
	lawyersList          []uint
	trialsList           []uint
	maxCapacityPerLawyer int
	distanceWeight       float64
	costWeight           float64
	timeWeight           float64
	balanceWeight        float64
	currentBalanceWeight float64
	populationSize       int
	generations          int
	crossoverRate        float64
	mutationRate         float64
	eliteRate            float64

	// 新增：存储完整解决方案信息
	lastCalculatedSolutions []GAScheduleSolution

	// 数据存储
	lawyers []model.Lawyer
	courts  []model.Court
	trials  []model.Trial
	routes  []model.Routes

	// 映射表
	lawyerMap map[uint]*model.Lawyer
	courtMap  map[uint]*model.Court
	trialMap  map[uint]*model.Trial
	routeMap  map[string]*model.Route

	// 冲突检查 - 使用更节省内存的方式
	conflicts map[string]bool

	// 预计算的费用矩阵
	currentCosts [][]float64

	// 连续出差配置
	continuousTrialConfig ContinuousTrialConfig
}

// GAScheduleSolution 遗传算法解决方案
type GAScheduleSolution struct {
	Matching      map[uint]uint
	MatchCount    int
	TotalCost     float64
	TotalDistance int
	TotalDuration int
	ConfigName    string
}

// NewGeneticAlgorithmScheduler 创建遗传算法调度器
func NewGeneticAlgorithmScheduler(mapModel imap.MapModel, lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) BestSchedule {
	scheduler := &GeneticAlgorithmScheduler{
		mapModel:             mapModel,
		maxCapacityPerLawyer: 5,
		distanceWeight:       1.0,
		costWeight:           1.0,
		timeWeight:           10.0,
		balanceWeight:        100.0,
		populationSize:       200,
		generations:          100,
		mutationRate:         0.1,
		crossoverRate:        0.7,
		// 默认连续出差配置
		continuousTrialConfig: ContinuousTrialConfig{
			Enable:               false, // 默认关闭
			SameCityBonus:        1000.0,
			CrossCityBonus:       500.0,
			MaxTravelTimeBetween: 180, // 3小时
			MinTimeBetweenTrials: 30,  // 30分钟
			MaxTimeBetweenTrials: 48,  // 48小时
		},
	}

	scheduler.initMaps(lawyers, courts, trials, routes)
	scheduler.initListsAndConflicts()

	return scheduler
}

// initMaps 初始化各种映射表
func (s *GeneticAlgorithmScheduler) initMaps(lawyers []model.Lawyer, courts []model.Court, trials []model.Trial, routes []model.Routes) {
	// 初始化律师映射
	s.lawyers = lawyers
	s.lawyerMap = make(map[uint]*model.Lawyer)
	for i := range lawyers {
		s.lawyerMap[lawyers[i].ID] = &lawyers[i]
	}

	// 初始化法院映射
	s.courts = courts
	s.courtMap = make(map[uint]*model.Court)
	for i := range courts {
		s.courtMap[courts[i].ID] = &courts[i]
	}

	// 初始化案件映射
	s.trials = trials
	s.trialMap = make(map[uint]*model.Trial)
	for i := range trials {
		s.trialMap[trials[i].ID] = &trials[i]
	}

	// 初始化路径映射
	s.routes = routes
	s.routeMap = make(map[string]*model.Route)
	for i := range routes {
		key := s.getRouteKey(routes[i].GetFromLocation(), routes[i].GetToLocation())
		s.routeMap[key] = &model.Route{
			Distance: routes[i].Distance,
			Duration: routes[i].Duration,
			Price:    int(routes[i].Cost),
		}
	}
}

// getRouteKey 生成路径的唯一键
func (s *GeneticAlgorithmScheduler) getRouteKey(from, to *model.Location) string {
	return fmt.Sprintf("%s:%s", from.String(), to.String())
}

// initListsAndConflicts 初始化列表和冲突矩阵
func (s *GeneticAlgorithmScheduler) initListsAndConflicts() {
	// 案件列表
	s.trialsList = make([]uint, 0, len(s.trialMap))
	for id := range s.trialMap {
		s.trialsList = append(s.trialsList, id)
	}
	sort.Slice(s.trialsList, func(i, j int) bool { return s.trialsList[i] < s.trialsList[j] })

	// 律师列表
	s.lawyersList = make([]uint, 0, len(s.lawyerMap))
	for id := range s.lawyerMap {
		s.lawyersList = append(s.lawyersList, id)
	}
	sort.Slice(s.lawyersList, func(i, j int) bool { return s.lawyersList[i] < s.lawyersList[j] })

	numTrials := len(s.trialsList)
	numLawyers := len(s.lawyersList)

	// 初始化冲突映射 - 使用更节省内存的方式
	s.conflicts = make(map[string]bool)

	for l := 0; l < numLawyers; l++ {
		lawyerID := s.lawyersList[l]
		for t1 := 0; t1 < numTrials; t1++ {
			trial1ID := s.trialsList[t1]
			trial1 := s.trialMap[trial1ID]
			start1, end1, err1 := s.calculateTravelTime(lawyerID, trial1)
			if err1 != nil {
				continue
			}
			for t2 := t1 + 1; t2 < numTrials; t2++ {
				trial2ID := s.trialsList[t2]
				trial2 := s.trialMap[trial2ID]
				start2, end2, err2 := s.calculateTravelTime(lawyerID, trial2)
				if err2 != nil {
					continue
				}
				if s.isTimeOverlap(start1, end1, start2, end2) {
					// 使用字符串键存储冲突信息
					key1 := s.getConflictKey(lawyerID, t1, t2)
					key2 := s.getConflictKey(lawyerID, t2, t1)
					s.conflicts[key1] = true
					s.conflicts[key2] = true
				}
			}
		}
	}
}

// calculateAssignmentCostSafe 安全计算案件-律师分配的费用
func (s *GeneticAlgorithmScheduler) calculateAssignmentCostSafe(trialID, lawyerID uint) CostResult {
	trial := s.trialMap[trialID]
	if trial == nil {
		return CostResult{0, false, fmt.Errorf("案件 %d 不存在", trialID)}
	}

	lawyer := s.lawyerMap[lawyerID]
	if lawyer == nil {
		return CostResult{0, false, fmt.Errorf("律师 %d 不存在", lawyerID)}
	}

	court := s.courtMap[trial.CourtID]
	if court == nil {
		return CostResult{0, false, fmt.Errorf("法院 %d 不存在", trial.CourtID)}
	}

	// 检查路径是否存在
	routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
	route, exists := s.routeMap[routeKey]
	if !exists {
		return CostResult{0, false, fmt.Errorf("路径不存在: %s", routeKey)}
	}
	if route.Distance == 0 {
		return CostResult{0, false, fmt.Errorf("路径距离为0: %s", routeKey)}
	}

	// 计算往返距离和时间
	distanceKm := float64(route.Distance*2) / 1000.0 // 往返距离转换为公里
	durationMinutes := float64(route.Duration * 2)   // 往返时间（数据库已存储为分钟）

	// 应用权重计算费用
	distanceCost := distanceKm * s.distanceWeight
	costCost := float64(route.Price) * s.costWeight
	timeCost := durationMinutes * s.timeWeight

	// 总费用
	totalCost := distanceCost + costCost + timeCost

	return CostResult{totalCost, true, nil}
}

// calculateAssignmentCost 计算案件-律师分配的费用
func (s *GeneticAlgorithmScheduler) calculateAssignmentCost(trialID, lawyerID uint) float64 {
	result := s.calculateAssignmentCostSafe(trialID, lawyerID)
	if !result.Valid {
		return math.Inf(1)
	}
	return result.Cost
}

// calculateTravelTime 计算出行时间
func (s *GeneticAlgorithmScheduler) calculateTravelTime(lawyerID uint, trial *model.Trial) (time.Time, time.Time, error) {
	lawyer := s.lawyerMap[lawyerID]
	court := s.courtMap[trial.CourtID]

	if lawyer == nil || court == nil {
		return time.Time{}, time.Time{}, fmt.Errorf("lawyer or court not found")
	}

	routeKey := s.getRouteKey(lawyer.GetLocation(), court.GetLocation())
	route, exists := s.routeMap[routeKey]
	if !exists || route.Distance == 0 {
		return time.Time{}, time.Time{}, fmt.Errorf("route not available")
	}

	// 计算出行时间（包含缓冲）
	travelDuration := time.Duration(float64(route.Duration) * Buffer)
	startTime := trial.StartTime.Add(-travelDuration * time.Minute)
	endTime := trial.EndTime.Add(travelDuration * time.Minute)

	return startTime, endTime, nil
}

// isTimeOverlap 检查时间重叠
func (s *GeneticAlgorithmScheduler) isTimeOverlap(start1, end1, start2, end2 time.Time) bool {
	return start1.Before(end2) && end1.After(start2)
}

// runGeneticAlgorithm 运行遗传算法
func (s *GeneticAlgorithmScheduler) runGeneticAlgorithm() []*Individual {
	// 初始化种群
	population := make([]*Individual, s.populationSize)
	for i := 0; i < s.populationSize; i++ {
		population[i] = s.createRandomIndividual()
	}

	// 进化
	for gen := 0; gen < s.generations; gen++ {
		// 创建新一代
		newPopulation := make([]*Individual, s.populationSize)

		// 保留最佳个体
		sort.Slice(population, func(i, j int) bool {
			return population[i].Fitness < population[j].Fitness
		})
		newPopulation[0] = population[0]

		// 生成其余个体
		for i := 1; i < s.populationSize; i++ {
			if rand.Float64() < s.crossoverRate {
				parent1 := s.selectParent(population)
				parent2 := s.selectParent(population)
				child := s.crossover(parent1, parent2)
				s.mutate(child)
				newPopulation[i] = child
			} else {
				parent := s.selectParent(population)
				child := &Individual{Genes: make([]int, len(parent.Genes))}
				copy(child.Genes, parent.Genes)
				s.mutate(child)
				newPopulation[i] = child
			}
		}

		population = newPopulation
	}

	// 返回最佳个体
	sort.Slice(population, func(i, j int) bool {
		return population[i].Fitness < population[j].Fitness
	})

	// 返回前10个最佳个体
	if len(population) > 10 {
		return population[:10]
	}
	return population
}

// Schedule 主调度方法
func (s *GeneticAlgorithmScheduler) Schedule() map[uint][]map[uint]uint {
	fmt.Printf("===== 遗传算法调度器开始调度 =====\n")
	fmt.Printf("律师数量: %d, 法院数量: %d, 案件数量: %d, 路径数量: %d\n",
		len(s.lawyerMap), len(s.courtMap), len(s.trialMap), len(s.routeMap))

	// 使用默认权重配置
	configs := []GAWeightConfig{
		{"距离优先", 10.0, 1.0, 1.0, 0.1},
		{"时间优先", 1.0, 1.0, 10.0, 0.1},
		{"负载均衡", 2.0, 2.0, 2.0, 10.0},
		{"均衡优化", 5.0, 5.0, 5.0, 1.0},
		{"远距离惩罚", 20.0, 2.0, 2.0, 0.5},
		{"快速到达", 2.0, 1.0, 15.0, 0.5},
		{"工作量平衡", 3.0, 3.0, 3.0, 8.0},
		{"距离敏感", 15.0, 3.0, 3.0, 1.0},
		{"时间敏感", 3.0, 1.0, 12.0, 1.0},
		{"综合平衡", 6.0, 6.0, 6.0, 3.0},
	}

	return s.ScheduleWithCustomWeights(configs)
}

// ScheduleWithCustomWeights 使用自定义权重配置进行调度
func (s *GeneticAlgorithmScheduler) ScheduleWithCustomWeights(customConfigs []GAWeightConfig) map[uint][]map[uint]uint {
	if len(customConfigs) == 0 {
		return map[uint][]map[uint]uint{0: {{}}}
	}

	allSolutions := make([]GAScheduleSolution, 0)

	for i, config := range customConfigs {
		fmt.Printf("\n正在计算方案 %d: %s\n", i+1, config.Name)

		// 设置权重
		s.distanceWeight = config.DistWeight
		s.costWeight = config.CostWeight
		s.timeWeight = config.TimeWeight
		s.currentBalanceWeight = config.BalanceWeight

		// 计算当前费用矩阵
		numTrials := len(s.trialsList)
		numLawyers := len(s.lawyersList)
		s.currentCosts = make([][]float64, numTrials)
		for t := 0; t < numTrials; t++ {
			s.currentCosts[t] = make([]float64, numLawyers)
			for l := 0; l < numLawyers; l++ {
				s.currentCosts[t][l] = s.calculateAssignmentCost(s.trialsList[t], s.lawyersList[l])
			}
		}

		// 运行遗传算法
		bestIndividuals := s.runGeneticAlgorithm()

		// 提取有效解并强制执行无时间冲突
		validSolutionCount := 0
		for _, individual := range bestIndividuals {
			if individual.Fitness >= 0 { // 有惩罚，跳过
				continue
			}

			rawMatching := s.extractMatching(individual)
			if len(rawMatching) == 0 {
				continue
			}

			// 强制执行无时间冲突
			conflictFreeMatching := s.enforceNoTimeConflicts(rawMatching)
			if len(conflictFreeMatching) == 0 {
				continue
			}

			// 最终验证
			if !s.validateNoTimeConflicts(conflictFreeMatching) {
				fmt.Printf("⚠️  方案 %s 经过冲突处理后仍存在时间冲突，跳过\n", config.Name)
				continue
			}

			distance, duration := s.calculateMetrics(conflictFreeMatching)
			actualTotalCost := s.calculateActualCost(conflictFreeMatching)

			solution := GAScheduleSolution{
				Matching:      conflictFreeMatching,
				MatchCount:    len(conflictFreeMatching),
				TotalCost:     actualTotalCost,
				TotalDistance: distance,
				TotalDuration: duration,
				ConfigName:    config.Name,
			}

			if !s.isDuplicateGASolution(solution, allSolutions) {
				allSolutions = append(allSolutions, solution)
				validSolutionCount++
			}
		}

		fmt.Printf("  配置 %s 生成了 %d 个无冲突方案\n", config.Name, validSolutionCount)
	}

	fmt.Printf("\n总共生成了 %d 个无冲突方案\n", len(allSolutions))
	return s.selectBestGASolutions(allSolutions)
}

// selectBestGASolutions 选择最佳方案
func (s *GeneticAlgorithmScheduler) selectBestGASolutions(solutions []GAScheduleSolution) map[uint][]map[uint]uint {
	if len(solutions) == 0 {
		return map[uint][]map[uint]uint{0: {{}}}
	}

	// 首先验证所有解决方案是否真的没有时间冲突
	validSolutions := make([]GAScheduleSolution, 0)
	for _, solution := range solutions {
		if s.validateNoTimeConflicts(solution.Matching) {
			validSolutions = append(validSolutions, solution)
		} else {
			fmt.Printf("⚠️  发现并过滤了一个有时间冲突的方案（匹配数：%d）\n", solution.MatchCount)
		}
	}

	if len(validSolutions) == 0 {
		fmt.Printf("⚠️  所有方案都有时间冲突，返回空结果\n")
		return map[uint][]map[uint]uint{0: {{}}}
	}

	// 按匹配数量降序，费用升序排序
	sort.Slice(validSolutions, func(i, j int) bool {
		if validSolutions[i].MatchCount != validSolutions[j].MatchCount {
			return validSolutions[i].MatchCount > validSolutions[j].MatchCount
		}
		return validSolutions[i].TotalCost < validSolutions[j].TotalCost
	})

	// 选择top10
	maxSolutions := 10
	if len(validSolutions) < maxSolutions {
		maxSolutions = len(validSolutions)
	}

	topSolutions := validSolutions[:maxSolutions]

	// 存储完整解决方案信息以供后续使用
	s.lastCalculatedSolutions = topSolutions

	// 按匹配数量分组
	result := make(map[uint][]map[uint]uint)
	for _, solution := range topSolutions {
		matchCount := uint(solution.MatchCount)
		result[matchCount] = append(result[matchCount], solution.Matching)
	}

	// 最终验证每个结果
	fmt.Printf("===== 最终时间冲突验证 =====\n")
	for matchCount, schedules := range result {
		for i, schedule := range schedules {
			if !s.validateNoTimeConflicts(schedule) {
				fmt.Printf("❌ 匹配数 %d 的方案 %d 存在时间冲突！\n", matchCount, i+1)
				// 从结果中移除这个方案
				result[matchCount] = append(schedules[:i], schedules[i+1:]...)
			}
		}
	}

	// 按照 md 格式输出结果
	s.printDetailedResults(result, topSolutions)

	return result
}

// isDuplicateGASolution 检查重复方案
func (s *GeneticAlgorithmScheduler) isDuplicateGASolution(solution GAScheduleSolution, solutions []GAScheduleSolution) bool {
	for _, existing := range solutions {
		if s.isGAMatchingEqual(solution.Matching, existing.Matching) {
			return true
		}
	}
	return false
}

// isGAMatchingEqual 检查匹配是否相等
func (s *GeneticAlgorithmScheduler) isGAMatchingEqual(m1, m2 map[uint]uint) bool {
	if len(m1) != len(m2) {
		return false
	}

	for k, v1 := range m1 {
		v2, exists := m2[k]
		if !exists || v1 != v2 {
			return false
		}
	}
	return true
}

// printDetailedResults 打印详细结果
func (s *GeneticAlgorithmScheduler) printDetailedResults(result map[uint][]map[uint]uint, solutions []GAScheduleSolution) {
	// 按匹配数量分组输出
	matchCounts := make([]uint, 0, len(result))
	for matchCount := range result {
		matchCounts = append(matchCounts, matchCount)
	}
	sort.Slice(matchCounts, func(i, j int) bool {
		return matchCounts[i] > matchCounts[j]
	})

	fmt.Printf("===== 遗传算法调度结果 =====\n")
	fmt.Printf("✅ 所有方案均已通过严格的时间冲突检查\n")
	fmt.Printf("📋 总共找到 %d 个不同匹配数量的方案组\n", len(matchCounts))

	for _, matchCount := range matchCounts {
		schedules := result[matchCount]
		fmt.Printf("\n📊 匹配数量 %d: 找到 %d 个方案\n", matchCount, len(schedules))

		// 为每个方案进行详细的时间冲突验证和显示
		solutionIndex := 0
		for _, solution := range solutions {
			if uint(solution.MatchCount) == matchCount {
				solutionIndex++

				// 进行详细的时间冲突验证
				isValid, validationErrors := s.performDetailedValidation(solution.Matching)
				if !isValid {
					fmt.Printf("❌ 方案 %d 发现时间冲突，跳过显示\n", solutionIndex)
					for _, err := range validationErrors {
						fmt.Printf("    - %s\n", err)
					}
					continue
				}

				fmt.Printf("✅ 方案 %d 通过时间冲突验证\n", solutionIndex)
				s.printSolutionDetails(solutionIndex, solution)
			}
		}
	}
}

// printSolutionDetails 打印方案详细信息
func (s *GeneticAlgorithmScheduler) printSolutionDetails(index int, solution GAScheduleSolution) {
	// 转换时间单位：从秒转换为分钟
	totalTimeMinutes := solution.TotalDuration / 60

	fmt.Printf("  方案 %d: %v 总路程: %d米 总时间: %d分钟\n",
		index, solution.Matching, solution.TotalDistance, totalTimeMinutes)
}

// GetLastCalculatedSolutions 获取最后计算的完整解决方案
func (s *GeneticAlgorithmScheduler) GetLastCalculatedSolutions() []GAScheduleSolution {
	return s.lastCalculatedSolutions
}

// SetMaxCapacityPerLawyer 设置每个律师最大处理案件数
func (s *GeneticAlgorithmScheduler) SetMaxCapacityPerLawyer(maxCapacity int) {
	s.maxCapacityPerLawyer = maxCapacity
}

// SetContinuousTrialConfig 设置连续出差配置
func (s *GeneticAlgorithmScheduler) SetContinuousTrialConfig(config ContinuousTrialConfig) {
	s.continuousTrialConfig = config
}

// GetContinuousTrialConfig 获取连续出差配置
func (s *GeneticAlgorithmScheduler) GetContinuousTrialConfig() ContinuousTrialConfig {
	return s.continuousTrialConfig
}
