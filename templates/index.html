<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-container {
            min-height: 500px;
        }
        .action-buttons {
            margin-bottom: 20px;
        }
        .search-box {
            max-width: 400px;
        }
        .search-box #clearSearchBtn {
            border-left: 0;
            border-radius: 0;
            background-color: #fff;
            border-color: #ced4da;
            color: #6c757d;
            transition: all 0.15s ease-in-out;
        }
        .search-box #clearSearchBtn:hover {
            background-color: #f8f9fa;
            color: #dc3545;
        }
        .trial-status-wrapper {
            display: inline-flex;
            align-items: center;
        }
        .status-help {
            cursor: help;
            transition: opacity 0.2s ease;
            position: relative;
        }
        .status-help:hover {
            opacity: 1 !important;
        }
        .custom-tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 5px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        .custom-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        .status-help:hover .custom-tooltip {
            opacity: 1;
            visibility: visible;
        }
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .dropdown-item {
            cursor: pointer;
        }
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
        .position-relative .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            z-index: 1000;
            display: none;
            min-width: 100%;
            padding: 0.5rem 0;
            margin: 0;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            background-color: #fff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .position-relative .dropdown-menu.show {
            display: block;
        }

        /* 遗传算法调度样式 */
        .schedule-cell {
            position: relative;
            min-height: 120px;
            min-width: 180px;
            padding: 10px;
            vertical-align: top;
        }

        .trial-block {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 6px;
            margin-bottom: 4px;
            font-size: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trial-block:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .trial-travel {
            color: #666;
            font-size: 11px;
            margin: 1px 0;
        }

        .trial-court {
            color: #1976d2;
            font-weight: bold;
            margin: 2px 0;
        }

        .trial-time {
            color: #424242;
            font-size: 11px;
        }

        .trial-tooltip {
            position: absolute;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            pointer-events: none;
        }

        .trial-block:hover .trial-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .genetic-stats {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .genetic-stats .stat-item {
            text-align: center;
            padding: 10px;
        }

        .genetic-stats .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }

        .genetic-stats .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        /* 新增样式 - 遗传算法匹配页面 */
        .trial-stack {
            display: flex;
            flex-direction: column;
            gap: 3px;
            padding: 4px;
        }
        
        .trial-layer {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 4px 6px;
            font-size: 11px;
            position: relative;
            margin: 1px 0;
            min-height: 22px;
            max-height: 35px;
            overflow: hidden;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .trial-layer.travel-go {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-color: #28a745;
            color: #155724;
        }
        
        .trial-layer.court-session {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #2196f3;
            color: #0d47a1;
            font-weight: bold;
        }
        
        .trial-layer.travel-back {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffc107;
            color: #856404;
        }

        .trial-layer .trial-time {
            display: block;
            font-weight: bold;
            line-height: 1.1;
            font-size: 10px;
        }

        .trial-layer .trial-court {
            display: block;
            font-weight: bold;
            line-height: 1.1;
            margin-bottom: 1px;
            font-size: 10px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .trial-layer small {
            font-size: 9px;
            line-height: 1.1;
        }
        
        .lawyer-name {
            font-weight: bold;
            min-width: 160px;
            max-width: 220px;
            padding: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }
        
        .lawyer-name:hover {
            background-color: #f8f9fa;
        }
        
        .trial-count {
            color: #666;
            font-size: 0.9em;
        }
        
        .schedule-empty {
            color: #999;
            font-style: italic;
        }
        
        .schedule-table-container {
            min-height: 400px;
        }

        /* 固定列表格样式 */
        .schedule-table-wrapper {
            position: relative;
            display: flex;
            min-height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
        }

        .fixed-column {
            width: 200px;
            min-width: 200px;
            background: #fff;
            z-index: 2;
            border-right: 2px solid #dee2e6;
            overflow-y: hidden;
            overflow-x: hidden;
        }

        .fixed-column::-webkit-scrollbar {
            width: 8px;
        }

        .fixed-column::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .fixed-column::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .fixed-column::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .fixed-column table {
            width: 100%;
            margin-bottom: 0;
        }

        .fixed-column th,
        .fixed-column td {
            width: 200px;
            min-width: 200px;
            max-width: 200px;
            border-right: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .scrollable-columns {
            flex: 1;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .scrollable-columns::-webkit-scrollbar {
            height: 8px;
            width: 8px;
        }

        .scrollable-columns::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .scrollable-columns::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .scrollable-columns::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .scrollable-columns table {
            min-width: max-content;
            margin-bottom: 0;
        }

        .scrollable-columns th,
        .scrollable-columns td {
            min-width: 200px;
            max-width: 200px;
            border-left: none;
        }

        /* 确保表格行高度一致 */
        .fixed-column tr,
        .scrollable-columns tr {
            height: 120px;
        }

        .fixed-column th,
        .scrollable-columns th {
            height: 60px;
            vertical-align: middle;
        }

        .fixed-column td,
        .scrollable-columns td {
            height: 120px;
            vertical-align: top;
            position: relative;
        }

        /* 试用栈样式，确保内容在单元格内正确对齐 */
        .trial-stack {
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 110px;
            justify-content: flex-start;
            align-items: stretch;
            gap: 2px;
            padding: 2px;
        }

        .schedule-cell {
            overflow: hidden;
            padding: 2px !important;
        }

        .schedule-cell .trial-stack {
            max-height: 110px;
            overflow-y: hidden;
        }

        .schedule-cell .trial-stack::-webkit-scrollbar {
            width: 4px;
        }

        .schedule-cell .trial-stack::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .schedule-cell .trial-stack::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }

        .lawyer-info {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
        }
        
        .solution-stats {
            font-size: 14px;
            font-weight: bold;
        }
        
        .lawyer-name:hover {
            background-color: #e3f2fd;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .trial-layer:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clickable:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 模态框样式 */
        .modal-content {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }
        
        .modal-header .btn-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: white;
            opacity: 0.8;
        }
        
        .modal-header .btn-close:hover {
            opacity: 1;
        }
        
        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .modal-body h6 {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .modal-body .table th {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            font-weight: 600;
        }
        
        .modal-body .table td {
            border-color: #dee2e6;
        }
        
        .modal-body .table-striped tbody tr:nth-of-type(odd) {
            background-color: #f8f9fa;
        }

        .lawyer-name {
            font-weight: bold;
            color: #1976d2;
            white-space: nowrap;
        }

        .trial-count {
            color: #666;
            font-size: 12px;
        }

        .schedule-empty {
            color: #999;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="fas fa-gavel"></i> {{.title}}
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <!-- 搜索框 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group search-box position-relative">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索ID/名称/地址...">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn" style="display: none;" title="清空搜索">
                                <i class="fas fa-times"></i>
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-success" id="addBtn">
                            <i class="fas fa-plus"></i> 添加
                        </button>
                        <button class="btn btn-warning" id="batchEditBtn">
                            <i class="fas fa-edit"></i> 批量编辑
                        </button>
                        <button class="btn btn-danger" id="batchDeleteBtn">
                            <i class="fas fa-trash"></i> 批量删除
                        </button>
                    </div>
                </div>

                <!-- Tab导航 -->
                <ul class="nav nav-tabs justify-content-between" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="genetic-tab" data-bs-toggle="tab" data-bs-target="#genetic" type="button" role="tab">
                            <i class="fas fa-brain"></i> 智能调度
                        </button>
                    </li>
                    <div class="d-flex ms-auto">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="lawyers-tab" data-bs-toggle="tab" data-bs-target="#lawyers" type="button" role="tab">
                                <i class="fas fa-user-tie"></i> 律师
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="courts-tab" data-bs-toggle="tab" data-bs-target="#courts" type="button" role="tab">
                                <i class="fas fa-university"></i> 法院
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="trials-tab" data-bs-toggle="tab" data-bs-target="#trials" type="button" role="tab">
                                <i class="fas fa-gavel"></i> 开庭
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="routes-tab" data-bs-toggle="tab" data-bs-target="#routes" type="button" role="tab">
                                <i class="fas fa-route"></i> 路线
                            </button>
                        </li>
                    </div>
                </ul>

                <!-- Tab内容 -->
                <div class="tab-content" id="mainTabsContent">
                    <!-- 律师Tab -->
                    <div class="tab-pane fade" id="lawyers" role="tabpanel">
                        <!-- 第一层：搜索和状态选择 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group search-box">
                                    <input type="text" class="form-control" id="lawyersSearchInput" placeholder="搜索ID/姓名/地址...">
                                    <button class="btn btn-outline-secondary" type="button" id="lawyersClearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="lawyersStatusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary" id="lawyersAddBtn">
                                        <i class="fas fa-plus"></i> 新增
                                    </button>
                                    <button type="button" class="btn btn-warning" id="lawyersBatchEditBtn">
                                        <i class="fas fa-edit"></i> 批量编辑
                                    </button>
                                    <button type="button" class="btn btn-danger" id="lawyersBatchDeleteBtn">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中间层：表格内容 -->
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><input type="checkbox" id="selectAllLawyers"></th>
                                            <th>ID</th>
                                            <th>姓名</th>
                                            <th>电话</th>
                                            <th>身份证</th>
                                            <th>雇佣关系</th>
                                            <th>职能</th>
                                            <th>状态</th>
                                            <th>地址</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="lawyersTableBody">
                                        <tr>
                                            <td colspan="10" class="text-center">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 底层：分页信息 -->
                        <div class="pagination-container" id="lawyersPaginationContainer">
                            <div class="d-flex align-items-center">
                                <span class="me-2">每页显示:</span>
                                <select class="form-select" id="lawyersPageSizeSelect" style="width: auto;">
                                    <option value="20">20</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                                <span class="ms-3" id="lawyersDataInfo">共 0 条数据</span>
                            </div>
                            <nav aria-label="分页导航">
                                <ul class="pagination mb-0" id="lawyersPagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="prev">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#" data-page="1">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="next">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 法院Tab -->
                    <div class="tab-pane fade" id="courts" role="tabpanel">
                        <!-- 第一层：搜索和状态选择 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group search-box">
                                    <input type="text" class="form-control" id="courtsSearchInput" placeholder="搜索ID/名称/地址...">
                                    <button class="btn btn-outline-secondary" type="button" id="courtsClearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="courtsStatusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary" id="courtsAddBtn">
                                        <i class="fas fa-plus"></i> 新增
                                    </button>
                                    <button type="button" class="btn btn-warning" id="courtsBatchEditBtn">
                                        <i class="fas fa-edit"></i> 批量编辑
                                    </button>
                                    <button type="button" class="btn btn-danger" id="courtsBatchDeleteBtn">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中间层：表格内容 -->
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><input type="checkbox" id="selectAllCourts"></th>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>状态</th>
                                            <th>省份</th>
                                            <th>城市</th>
                                            <th>区县</th>
                                            <th>地址</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="courtsTableBody">
                                        <tr>
                                            <td colspan="9" class="text-center">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 底层：分页信息 -->
                        <div class="pagination-container" id="courtsPaginationContainer">
                            <div class="d-flex align-items-center">
                                <span class="me-2">每页显示:</span>
                                <select class="form-select" id="courtsPageSizeSelect" style="width: auto;">
                                    <option value="20">20</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                                <span class="ms-3" id="courtsDataInfo">共 0 条数据</span>
                            </div>
                            <nav aria-label="分页导航">
                                <ul class="pagination mb-0" id="courtsPagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="prev">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#" data-page="1">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="next">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 开庭Tab -->
                    <div class="tab-pane fade" id="trials" role="tabpanel">
                        <!-- 第一层：搜索和状态选择 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group search-box">
                                    <input type="text" class="form-control" id="trialsSearchInput" placeholder="搜索ID/案件ID/法院ID...">
                                    <button class="btn btn-outline-secondary" type="button" id="trialsClearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="trialsStatusFilter">
                                    <option value="">全部状态</option>
                                    <option value="0">📄 待登记</option>
                                    <option value="1">📅 待排庭</option>
                                    <option value="2">🚗 待滴滴</option>
                                    <option value="3">💳 待支付</option>
                                    <option value="4">⏰ 待开庭</option>
                                    <option value="5">❌ 开庭缺席</option>
                                    <option value="6">✅ 已开庭</option>
                                    <option value="7">⭕ 已取消</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary" id="trialsAddBtn">
                                        <i class="fas fa-plus"></i> 新增
                                    </button>
                                    <button type="button" class="btn btn-warning" id="trialsBatchEditBtn">
                                        <i class="fas fa-edit"></i> 批量编辑
                                    </button>
                                    <button type="button" class="btn btn-danger" id="trialsBatchDeleteBtn">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中间层：表格内容 -->
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><input type="checkbox" id="selectAllTrials"></th>
                                            <th>ID</th>
                                            <th>案件ID</th>
                                            <th>法院ID</th>
                                            <th>状态</th>
                                            <th>开庭方式</th>
                                            <th>庭审类型</th>
                                            <th>开始时间</th>
                                            <th>结束时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="trialsTableBody">
                                        <tr>
                                            <td colspan="10" class="text-center">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 底层：分页信息 -->
                        <div class="pagination-container" id="trialsPaginationContainer">
                            <div class="d-flex align-items-center">
                                <span class="me-2">每页显示:</span>
                                <select class="form-select" id="trialsPageSizeSelect" style="width: auto;">
                                    <option value="20">20</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                                <span class="ms-3" id="trialsDataInfo">共 0 条数据</span>
                            </div>
                            <nav aria-label="分页导航">
                                <ul class="pagination mb-0" id="trialsPagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="prev">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#" data-page="1">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="next">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 路线Tab -->
                    <div class="tab-pane fade" id="routes" role="tabpanel">
                        <!-- 第一层：搜索和状态选择 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group search-box">
                                    <input type="text" class="form-control" id="routesSearchInput" placeholder="搜索ID/类型/地址...">
                                    <button class="btn btn-outline-secondary" type="button" id="routesClearSearchBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="routesStatusFilter">
                                    <option value="">全部状态</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                </select>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary" id="routesAddBtn">
                                        <i class="fas fa-plus"></i> 新增
                                    </button>
                                    <button type="button" class="btn btn-warning" id="routesBatchEditBtn">
                                        <i class="fas fa-edit"></i> 批量编辑
                                    </button>
                                    <button type="button" class="btn btn-danger" id="routesBatchDeleteBtn">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中间层：表格内容 -->
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th><input type="checkbox" id="selectAllRoutes"></th>
                                            <th>ID</th>
                                            <th>类型</th>
                                            <th>出发地</th>
                                            <th>目的地</th>
                                            <th>距离(m)</th>
                                            <th>时间(s)</th>
                                            <th>成本</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="routesTableBody">
                                        <tr>
                                            <td colspan="10" class="text-center">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 底层：分页信息 -->
                        <div class="pagination-container" id="routesPaginationContainer">
                            <div class="d-flex align-items-center">
                                <span class="me-2">每页显示:</span>
                                <select class="form-select" id="routesPageSizeSelect" style="width: auto;">
                                    <option value="20">20</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                                <span class="ms-3" id="routesDataInfo">共 0 条数据</span>
                            </div>
                            <nav aria-label="分页导航">
                                <ul class="pagination mb-0" id="routesPagination">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="prev">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#" data-page="1">1</a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" data-page="next">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>

                    <!-- 智能调度Tab -->
                    <div class="tab-pane fade show active" id="genetic" role="tabpanel">
                        <div class="container-fluid">
                            <!-- 第一行：权重配置控制面板 -->
                            <div class="row mb-3 align-items-end">
                                <div class="col-md-2">
                                    <label class="form-label">权重配置</label>
                                    <select class="form-select" id="geneticConfigSelect">
                                        <option value="">选择权重配置</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">距离权重</label>
                                    <input type="number" class="form-control" id="distanceWeight" step="0.1" min="0" value="1.0">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">费用权重</label>
                                    <input type="number" class="form-control" id="costWeight" step="0.1" min="0" value="1.0">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">时间权重</label>
                                    <input type="number" class="form-control" id="timeWeight" step="0.1" min="0" value="1.0">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">工作负载权重</label>
                                    <input type="number" class="form-control" id="workloadWeight" step="0.1" min="0" value="1.0">
                                </div>
                                <div class="col-md-2 text-end">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-block">
                                        <button class="btn btn-success w-100" id="runGeneticBtn">
                                            <i class="fas fa-play"></i> 重新计算
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：方案选择和地图查看 -->
                            <div class="row mb-3 align-items-end" id="solutionRow" style="display: none;">
                                <div class="col-md-6">
                                    <label class="form-label">方案选择</label>
                                    <select class="form-select" id="solutionSelect">
                                        <option value="">选择方案</option>
                                    </select>
                                </div>
                                <div class="col-md-6 text-end">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-block">
                                        <button class="btn btn-primary" id="showMapBtn">
                                            <i class="fas fa-map"></i> 地图显示
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 第三部分：时间表格 -->
                            <div class="row" id="scheduleTableContainer" style="display: none;">
                                <div class="col-12">
                                    <div class="schedule-table-wrapper">
                                        <div class="fixed-column">
                                            <table class="table table-bordered table-hover">
                                                <thead class="table-dark" id="scheduleTableHeadFixed">
                                                    <!-- 固定列表头 -->
                                                </thead>
                                                <tbody id="scheduleTableBodyFixed">
                                                    <!-- 固定列内容 -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="scrollable-columns">
                                            <table class="table table-bordered table-hover" id="scheduleTable">
                                                <thead class="table-dark" id="scheduleTableHead">
                                                    <!-- 动态生成表头 -->
                                                </thead>
                                                <tbody id="scheduleTableBody">
                                                    <!-- 动态生成表格内容 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">编辑</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div id="editFormFields"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量编辑模态框 -->
    <div class="modal fade" id="batchEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量编辑</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted mb-3">已选择 <span id="batchEditCount">0</span> 项进行编辑</p>
                    <form id="batchEditForm">
                        <div id="batchEditFormFields"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="batchEditSaveBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作确认模态框 -->
    <div class="modal fade" id="batchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchModalTitle">批量操作确认</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="batchModalMessage"></p>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="batchScope" id="batchScopePage" value="page" checked>
                        <label class="form-check-label" for="batchScopePage">当前页面</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="batchScope" id="batchScopeAll" value="all">
                        <label class="form-check-label" for="batchScopeAll">所有数据</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="batchConfirmBtn">确认</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentTab = 'genetic'; // 默认打开智能调度tab
        let selectedIds = [];
        
        // 各tab的独立状态管理
        let tabStates = {
            lawyers: { currentPage: 1, pageSize: 50, searchQuery: '', statusFilter: '', totalPages: 1 },
            courts: { currentPage: 1, pageSize: 50, searchQuery: '', statusFilter: '', totalPages: 1 },
            trials: { currentPage: 1, pageSize: 50, searchQuery: '', statusFilter: '', totalPages: 1 },
            routes: { currentPage: 1, pageSize: 50, searchQuery: '', statusFilter: '', totalPages: 1 }
        };
        
        // Cookie操作函数
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = name + "=" + value + ";expires=" + expires.toUTCString() + ";path=/";
        }
        
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
        
        // 保存tab状态到cookie
        function saveTabState(tabName) {
            const state = tabStates[tabName];
            setCookie(`${tabName}_pageSize`, state.pageSize, 30);
            setCookie(`${tabName}_statusFilter`, state.statusFilter, 30);
        }
        
        // 从cookie加载tab状态
        function loadTabState(tabName) {
            const pageSize = getCookie(`${tabName}_pageSize`);
            const statusFilter = getCookie(`${tabName}_statusFilter`);
            
            if (pageSize) {
                tabStates[tabName].pageSize = parseInt(pageSize);
                const pageSizeSelect = document.getElementById(`${tabName}PageSizeSelect`);
                if (pageSizeSelect) pageSizeSelect.value = pageSize;
            }
            
            if (statusFilter) {
                tabStates[tabName].statusFilter = statusFilter;
                const statusFilterSelect = document.getElementById(`${tabName}StatusFilter`);
                if (statusFilterSelect) statusFilterSelect.value = statusFilter;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载所有tab的状态
            Object.keys(tabStates).forEach(tabName => {
                loadTabState(tabName);
            });
            
            setupEventListeners();
            setupGeneticTab();
            
            // 默认加载智能调度tab
            currentTab = 'genetic';
        });

        // 切换清空按钮显示/隐藏
        function toggleClearButton() {
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.getElementById('clearSearchBtn');
            if (searchInput.value.trim() !== '') {
                clearBtn.style.display = 'block';
            } else {
                clearBtn.style.display = 'none';
            }
        }

        // 更新状态筛选器
        function updateStatusFilter() {
            const statusFilter = document.getElementById('statusFilter');
            const currentValue = statusFilter.value;
            
            if (currentTab === 'trials') {
                statusFilter.innerHTML = `
                    <option value="">全部状态</option>
                    <option value="0">📄 待登记</option>
                    <option value="1">📅 待排庭</option>
                    <option value="2">🚗 待滴滴</option>
                    <option value="3">💳 待支付</option>
                    <option value="4">⏰ 待开庭</option>
                    <option value="5">❌ 开庭缺席</option>
                    <option value="6">✅ 已开庭</option>
                    <option value="7">⭕ 已取消</option>
                `;
            } else {
                statusFilter.innerHTML = `
                    <option value="">全部状态</option>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                `;
            }
            
            // 尝试保留之前的选择
            statusFilter.value = currentValue;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // Tab切换
            const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabs.forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(e) {
                    const target = e.target.getAttribute('data-bs-target');
                    const newTab = target.replace('#', '');
                    currentTab = newTab;
                    selectedIds = [];
                    
                    if (newTab === 'genetic') {
                        setupGeneticTab();
                    } else {
                        // 加载对应tab的数据
                        loadTabData(newTab);
                    }
                });
            });
            
            // 为每个tab设置独立的事件监听器
            setupTabEventListeners('lawyers');
            setupTabEventListeners('courts');
            setupTabEventListeners('trials');
            setupTabEventListeners('routes');
                }
        
        // 为单个tab设置事件监听器
        function setupTabEventListeners(tabName) {
            // 搜索输入框
            const searchInput = document.getElementById(`${tabName}SearchInput`);
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    tabStates[tabName].searchQuery = this.value;
                    tabStates[tabName].currentPage = 1;
                    loadTabData(tabName);
                });
            }
            
            // 清空搜索按钮
            const clearBtn = document.getElementById(`${tabName}ClearSearchBtn`);
            if (clearBtn) {
                clearBtn.addEventListener('click', function() {
                    const searchInput = document.getElementById(`${tabName}SearchInput`);
                    if (searchInput) {
                        searchInput.value = '';
                        tabStates[tabName].searchQuery = '';
                        tabStates[tabName].currentPage = 1;
                        loadTabData(tabName);
                    }
                });
            }
            
            // 状态筛选器
            const statusFilter = document.getElementById(`${tabName}StatusFilter`);
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    tabStates[tabName].statusFilter = this.value;
                    tabStates[tabName].currentPage = 1;
                    saveTabState(tabName);
                    loadTabData(tabName);
                });
            }
            
            // 分页大小选择器
            const pageSizeSelect = document.getElementById(`${tabName}PageSizeSelect`);
            if (pageSizeSelect) {
                pageSizeSelect.addEventListener('change', function() {
                    tabStates[tabName].pageSize = parseInt(this.value);
                    tabStates[tabName].currentPage = 1;
                    saveTabState(tabName);
                    loadTabData(tabName);
                });
            }
            
            // 分页按钮
            const pagination = document.getElementById(`${tabName}Pagination`);
            if (pagination) {
                pagination.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (e.target.tagName === 'A') {
                        const page = e.target.getAttribute('data-page');
                        if (page === 'prev') {
                            if (tabStates[tabName].currentPage > 1) {
                                tabStates[tabName].currentPage--;
                                loadTabData(tabName);
                            }
                        } else if (page === 'next') {
                            if (tabStates[tabName].currentPage < tabStates[tabName].totalPages) {
                                tabStates[tabName].currentPage++;
                                loadTabData(tabName);
                            }
                        } else {
                            tabStates[tabName].currentPage = parseInt(page);
                            loadTabData(tabName);
                        }
                    }
                });
            }
            
            // 操作按钮
            const addBtn = document.getElementById(`${tabName}AddBtn`);
            if (addBtn) {
                addBtn.addEventListener('click', function() {
                    openEditModal(tabName);
                });
            }
            
            const batchEditBtn = document.getElementById(`${tabName}BatchEditBtn`);
            if (batchEditBtn) {
                batchEditBtn.addEventListener('click', function() {
                    if (selectedIds.length === 0) {
                        alert('请选择要编辑的项目');
                        return;
                    }
                    showBatchEditModal(tabName);
                });
            }
            
            const batchDeleteBtn = document.getElementById(`${tabName}BatchDeleteBtn`);
            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', function() {
                    if (selectedIds.length === 0) {
                        alert('请选择要删除的项目');
                        return;
                    }
                    showBatchModal('delete', `确定要删除选中的 ${selectedIds.length} 项吗？`, tabName);
                });
            }
            
            // 全选按钮
            const selectAllBtn = document.getElementById(`selectAll${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
            if (selectAllBtn) {
                selectAllBtn.addEventListener('change', function() {
                    toggleSelectAll(tabName);
                });
            }
        }
        
        // 加载指定tab的数据
        function loadTabData(tabName) {
            const state = tabStates[tabName];
            let url = `/api/${tabName}?page=${state.currentPage}&page_size=${state.pageSize}`;
            
            if (state.searchQuery) {
                url += `&search=${encodeURIComponent(state.searchQuery)}`;
            }
            
            if (state.statusFilter) {
                url += `&status=${state.statusFilter}`;
            }
            
            // 显示加载状态
            showTabLoadingState(tabName);
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    updateTabTable(tabName, data);
                    updateTabPagination(tabName, data);
                })
                .catch(error => {
                    console.error(`加载${tabName}数据失败:`, error);
                    showTabErrorState(tabName);
                });
        }
        
        // 显示tab加载状态
        function showTabLoadingState(tabName) {
            const tbody = document.getElementById(`${tabName}TableBody`);
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </td>
                    </tr>
                `;
            }
        }
        
        // 显示tab错误状态
        function showTabErrorState(tabName) {
            const tbody = document.getElementById(`${tabName}TableBody`);
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> 加载失败，请刷新重试
                        </td>
                    </tr>
                `;
            }
        }
        
        // 更新tab表格
        function updateTabTable(tabName, data) {
            const tbody = document.getElementById(`${tabName}TableBody`);
            if (!tbody) return;
            
            if (data.data && data.data.length > 0) {
                tbody.innerHTML = data.data.map(item => generateTableRow(tabName, item)).join('');
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="text-muted">暂无数据</div>
                        </td>
                    </tr>
                `;
            }
        }
        
        // 生成表格行
        function generateTableRow(tabName, item) {
            if (tabName === 'lawyers') {
                return `
                    <tr>
                        <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${item.phone}</td>
                        <td>${item.id_card}</td>
                        <td>${item.employment}</td>
                        <td>${item.ability}</td>
                        <td>${item.status === 1 ? '<i class="fas fa-check-circle text-success" title="启用"></i>' : '<i class="fas fa-times-circle text-danger" title="禁用"></i>'}</td>
                        <td>${item.formatted_address}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                        </td>
                    </tr>
                `;
            } else if (tabName === 'courts') {
                return `
                    <tr>
                        <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${item.status === 1 ? '<i class="fas fa-check-circle text-success" title="启用"></i>' : '<i class="fas fa-times-circle text-danger" title="禁用"></i>'}</td>
                        <td>${item.province}</td>
                        <td>${item.city}</td>
                        <td>${item.district}</td>
                        <td>${item.formatted_address}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                        </td>
                    </tr>
                `;
            } else if (tabName === 'trials') {
                return `
                    <tr>
                        <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                        <td>${item.id}</td>
                        <td>${item.case_id}</td>
                        <td>${item.court_id}</td>
                        <td>${getTrialStatus(item.status)}</td>
                        <td>${getTrialMode(item.mode)}</td>
                        <td>${getTrialType(item.type)}</td>
                        <td>${formatDateTime(item.start_time)}</td>
                        <td>${formatDateTime(item.end_time)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                        </td>
                    </tr>
                `;
            } else if (tabName === 'routes') {
                return `
                    <tr>
                        <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                        <td>${item.id}</td>
                        <td>${item.type}</td>
                        <td>${item.from_formatted_address}</td>
                        <td>${item.to_formatted_address}</td>
                        <td>${item.distance}</td>
                        <td>${item.duration}</td>
                        <td>${item.cost}</td>
                        <td>${getRouteStatus(item.status)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                        </td>
                    </tr>
                `;
            }
            return `<tr><td colspan="10">未知类型</td></tr>`;
        }
        
        // 更新tab分页
        function updateTabPagination(tabName, data) {
            const state = tabStates[tabName];
            state.totalPages = data.total_pages || 1;
            
            const dataInfo = document.getElementById(`${tabName}DataInfo`);
            if (dataInfo) {
                dataInfo.textContent = `共 ${data.total || 0} 条数据, 第 ${state.currentPage} 页, 共 ${state.totalPages} 页`;
            }
            
            const pagination = document.getElementById(`${tabName}Pagination`);
            if (pagination) {
                pagination.innerHTML = generatePaginationHTML(tabName, state.currentPage, state.totalPages);
            }
        }
        
        // 生成分页HTML
        function generatePaginationHTML(tabName, currentPage, totalPages) {
            let html = '';
            
            // 上一页
            html += `<li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="prev">上一页</a>
            </li>`;
            
            // 页码
            const maxVisiblePages = 7;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            
            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            // 第一页
            if (startPage > 1) {
                html += `<li class="page-item">
                    <a class="page-link" href="#" data-page="1">1</a>
                </li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
                }
            }
            
            // 中间页码
            for (let i = startPage; i <= endPage; i++) {
                html += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>`;
            }
            
            // 最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
                }
                html += `<li class="page-item">
                    <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
                </li>`;
            }
            
            // 下一页
            html += `<li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="next">下一页</a>
            </li>`;
            
            return html;
        }
        
        // 辅助函数
        function openEditModal(tabName, id = null) {
            console.log(`打开${tabName}编辑模态框`, id);
        }
        
        function showBatchEditModal(tabName) {
            console.log(`显示${tabName}批量编辑模态框`);
        }
        
        function showBatchModal(type, message, tabName) {
            console.log(`显示${tabName}批量${type}确认模态框:`, message);
        }
        
        function toggleSelectAll(tabName) {
            const selectAllCheckbox = document.getElementById(`selectAll${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
            const rowCheckboxes = document.querySelectorAll(`#${tabName}TableBody input[type="checkbox"]`);
            
            if (selectAllCheckbox.checked) {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    if (!selectedIds.includes(checkbox.value)) {
                        selectedIds.push(checkbox.value);
                    }
                });
            } else {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    const index = selectedIds.indexOf(checkbox.value);
                    if (index > -1) {
                        selectedIds.splice(index, 1);
                    }
                });
            }
        }
        
        // 编辑和删除函数（全局函数，供onclick调用）
        function editItem(id) {
            openEditModal(currentTab, id);
        }
        
        function deleteItem(id) {
            if (confirm('确定要删除这个项目吗？')) {
                console.log(`删除${currentTab}项目:`, id);
            }


        // 加载数据
        function loadData() {
            console.log('Loading data for tab:', currentTab);
            
            const endpoint = `/api/${currentTab}`;
            const params = new URLSearchParams({
                page: currentPage,
                page_size: pageSize,
                search: searchQuery,
                status: statusFilter
            });

            // 显示加载状态
            showLoadingState();

            fetch(`${endpoint}?${params}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Data loaded successfully:', data);
                    renderTable(data.data);
                    updatePagination(data);
                    updateDataInfo(data);
                })
                .catch(error => {
                    console.error('加载数据失败:', error);
                    showError('加载数据失败: ' + error.message);
                    // 出错时显示错误状态
                    showErrorState();
                });
        }

        // 显示加载状态
        function showLoadingState() {
            const tableBody = document.getElementById(`${currentTab}TableBody`);
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </td>
                    </tr>
                `;
            }
        }

                 // 显示错误状态
         function showErrorState() {
             const tableBody = document.getElementById(`${currentTab}TableBody`);
             if (tableBody) {
                 tableBody.innerHTML = `
                     <tr>
                         <td colspan="10" class="text-center text-danger">
                             <i class="fas fa-exclamation-triangle"></i> 加载失败，请刷新重试
                         </td>
                     </tr>
                 `;
             }
         }

        // 渲染表格
        function renderTable(data) {
            console.log('Rendering table for tab:', currentTab, 'with data:', data);
            
            const tableBody = document.getElementById(`${currentTab}TableBody`);
            if (!tableBody) {
                console.error('找不到表格容器:', `${currentTab}TableBody`);
                return;
            }
            
            tableBody.innerHTML = '';

            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="text-center">暂无数据</td></tr>';
                return;
            }

            data.forEach(item => {
                const row = createTableRow(item);
                if (row) {
                    tableBody.appendChild(row);
                }
            });
        }

        // 创建表格行
        function createTableRow(item) {
            const row = document.createElement('tr');
            
            if (currentTab === 'lawyers') {
                row.innerHTML = `
                    <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                    <td>${item.id}</td>
                    <td>${item.name}</td>
                    <td>${item.phone}</td>
                    <td>${item.id_card}</td>
                    <td>${item.employment}</td>
                    <td>${item.ability}</td>
                    <td>${item.status === 1 ? '<i class="fas fa-check-circle text-success" title="启用"></i>' : '<i class="fas fa-times-circle text-danger" title="禁用"></i>'}</td>
                    <td>${item.formatted_address}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                    </td>
                `;
            } else if (currentTab === 'courts') {
                row.innerHTML = `
                    <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                    <td>${item.id}</td>
                    <td>${item.name}</td>
                    <td>${item.status === 1 ? '<i class="fas fa-check-circle text-success" title="启用"></i>' : '<i class="fas fa-times-circle text-danger" title="禁用"></i>'}</td>
                    <td>${item.province}</td>
                    <td>${item.city}</td>
                    <td>${item.district}</td>
                    <td>${item.formatted_address}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                    </td>
                `;
            } else if (currentTab === 'trials') {
                row.innerHTML = `
                    <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                    <td>${item.id}</td>
                    <td>${item.case_id}</td>
                    <td>${item.court_id}</td>
                    <td>${getTrialStatus(item.status)}</td>
                    <td>${getTrialMode(item.mode)}</td>
                    <td>${getTrialType(item.type)}</td>
                    <td>${formatDateTime(item.start_time)}</td>
                    <td>${formatDateTime(item.end_time)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                    </td>
                `;
            } else if (currentTab === 'routes') {
                row.innerHTML = `
                    <td><input type="checkbox" class="row-select" value="${item.id}"></td>
                    <td>${item.id}</td>
                    <td>${item.type}</td>
                    <td>${item.from_formatted_address}</td>
                    <td>${item.to_formatted_address}</td>
                    <td>${item.distance}</td>
                    <td>${item.duration}</td>
                    <td>${item.cost}</td>
                    <td>${getRouteStatus(item.status)}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})">编辑</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem(${item.id})">删除</button>
                    </td>
                `;
            }

            return row;
        }

        // 更新分页
        function updatePagination(data) {
            totalPages = data.total_pages;
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            // 首页
            const firstDisabled = currentPage === 1 ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${firstDisabled}">
                    <a class="page-link" href="#" data-page="first">首页</a>
                </li>
            `;

            // 上一页
            const prevDisabled = currentPage === 1 ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="#" data-page="prev">上一页</a>
                </li>
            `;

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.innerHTML += `
                    <li class="page-item ${active}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }

            // 下一页
            const nextDisabled = currentPage === totalPages ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="#" data-page="next">下一页</a>
                </li>
            `;

            // 尾页
            const lastDisabled = currentPage === totalPages ? 'disabled' : '';
            pagination.innerHTML += `
                <li class="page-item ${lastDisabled}">
                    <a class="page-link" href="#" data-page="last">尾页</a>
                </li>
            `;
        }

        // 更新数据信息
        function updateDataInfo(data) {
            const dataInfo = document.getElementById('dataInfo');
            dataInfo.textContent = `共 ${data.total} 条数据，第 ${data.page} 页，共 ${data.total_pages} 页`;
        }

        // 辅助函数
        function getTrialStatus(status) {
            const statusMap = {
                0: { icon: '<i class="fas fa-file-alt text-secondary"></i>', text: '待登记' },
                1: { icon: '<i class="fas fa-calendar-plus text-info"></i>', text: '待排庭' },
                2: { icon: '<i class="fas fa-car text-warning"></i>', text: '待滴滴' },
                3: { icon: '<i class="fas fa-credit-card text-danger"></i>', text: '待支付' },
                4: { icon: '<i class="fas fa-clock text-primary"></i>', text: '待开庭' },
                5: { icon: '<i class="fas fa-user-times text-danger"></i>', text: '开庭缺席' },
                6: { icon: '<i class="fas fa-check-circle text-success"></i>', text: '已开庭' },
                7: { icon: '<i class="fas fa-times-circle text-secondary"></i>', text: '已取消' }
            };
            const statusInfo = statusMap[status] || { icon: '<i class="fas fa-question-circle text-secondary"></i>', text: '未知' };
            return `<span class="trial-status-wrapper">
                        ${statusInfo.icon}
                        <i class="fas fa-question-circle text-muted status-help" 
                           style="margin-left: 5px; font-size: 0.8em; opacity: 0.7;">
                           <span class="custom-tooltip">${statusInfo.text}</span>
                        </i>
                    </span>`;
        }

        function getTrialMode(mode) {
            const modeMap = {0: '待定', 1: '线上', 2: '线下'};
            return modeMap[mode] || '未知';
        }

        function getTrialType(type) {
            const typeMap = {1: '民初庭', 2: '调解庭'};
            return typeMap[type] || '未知';
        }

        function getRouteStatus(status) {
            const statusMap = {
                0: '<i class="fas fa-times-circle text-danger" title="禁用"></i>',
                1: '<i class="fas fa-check-circle text-success" title="可用"></i>',
                2: '<i class="fas fa-edit text-warning" title="人工修正"></i>'
            };
            return statusMap[status] || '<i class="fas fa-question-circle text-secondary" title="未知"></i>';
        }

        function formatDateTime(dateTime) {
            if (!dateTime) return '';
            return new Date(dateTime).toLocaleString('zh-CN');
        }

        function formatDateTimeForInput(dateTime) {
            if (!dateTime) return '';
            const date = new Date(dateTime);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        function showError(message) {
            alert(message);
        }

        function toggleSelectAll(tab) {
            const checkboxes = document.querySelectorAll(`#${tab} .row-select`);
            const selectAll = document.getElementById(`selectAll${tab.charAt(0).toUpperCase() + tab.slice(1)}`);
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateSelectedIds();
        }

        function updateSelectedIds() {
            const checkboxes = document.querySelectorAll('.row-select:checked');
            selectedIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
        }

        function showBatchModal(operation, message) {
            document.getElementById('batchModalTitle').textContent = '批量操作确认';
            document.getElementById('batchModalMessage').textContent = message;
            document.getElementById('batchModal').setAttribute('data-operation', operation);
            new bootstrap.Modal(document.getElementById('batchModal')).show();
        }

        function performBatchOperation() {
            const operation = document.getElementById('batchModal').getAttribute('data-operation');
            const scope = document.querySelector('input[name="batchScope"]:checked').value;
            
            const payload = {
                operation: operation,
                ids: selectedIds,
                scope: scope
            };

            fetch(`/api/${currentTab}/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    loadData();
                    selectedIds = [];
                } else {
                    alert('操作失败');
                }
            })
            .catch(error => {
                console.error('批量操作失败:', error);
                alert('操作失败');
            });

            bootstrap.Modal.getInstance(document.getElementById('batchModal')).hide();
        }

        function openEditModal(id = null) {
            const isEdit = id !== null;
            document.getElementById('editModalTitle').textContent = isEdit ? '编辑' : '添加';
            
            // 存储当前编辑的ID
            document.getElementById('editModal').setAttribute('data-edit-id', id || '');
            
            if (isEdit) {
                // 加载现有数据
                fetch(`/api/${currentTab}/${id}`)
                    .then(response => response.json())
                    .then(data => {
                        generateEditForm(data);
                        
                        // 初始化搜索功能
                        setTimeout(() => {
                            if (currentTab === 'trials') {
                                setupCourtSearch();
                                // 如果是编辑模式，设置法院名称
                                if (data.court_id) {
                                    fetch(`/api/courts/${data.court_id}`)
                                        .then(response => response.json())
                                        .then(court => {
                                            document.getElementById('courtSearch').value = `${court.name} (ID: ${court.id})`;
                                        })
                                        .catch(error => console.log('加载法院信息失败'));
                                }
                            } else if (currentTab === 'routes') {
                                setupRouteLocationSearch();
                            }
                        }, 100);
                        
                        new bootstrap.Modal(document.getElementById('editModal')).show();
                    })
                    .catch(error => {
                        console.error('加载数据失败:', error);
                        showError('加载数据失败');
                    });
            } else {
                generateEditForm();
                
                // 初始化搜索功能
                setTimeout(() => {
                    if (currentTab === 'trials') {
                        setupCourtSearch();
                    } else if (currentTab === 'routes') {
                        setupRouteLocationSearch();
                    }
                }, 100);
                
                new bootstrap.Modal(document.getElementById('editModal')).show();
            }
        }

        function generateEditForm(data = {}) {
            const formFields = document.getElementById('editFormFields');
            formFields.innerHTML = '';

            if (currentTab === 'lawyers') {
                formFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-control" name="name" value="${data.name || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">电话</label>
                        <input type="text" class="form-control" name="phone" value="${data.phone || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">身份证</label>
                        <input type="text" class="form-control" name="id_card" value="${data.id_card || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">雇佣关系</label>
                        <input type="text" class="form-control" name="employment" value="${data.employment || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">职能</label>
                        <input type="text" class="form-control" name="ability" value="${data.ability || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="1" ${data.status === 1 ? 'selected' : ''}>启用</option>
                            <option value="0" ${data.status === 0 ? 'selected' : ''}>禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">地址</label>
                        <div class="input-group">
                            <input type="text" class="form-control" name="original_address" value="${data.original_address || ''}" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="parseAddress('lawyers')">解析地址</button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">省份</label>
                        <input type="text" class="form-control" name="province" value="${data.province || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">城市</label>
                        <input type="text" class="form-control" name="city" value="${data.city || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">区县</label>
                        <input type="text" class="form-control" name="district" value="${data.district || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">经度</label>
                        <input type="number" class="form-control" name="lng" value="${data.lng || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">纬度</label>
                        <input type="number" class="form-control" name="lat" value="${data.lat || ''}" step="0.0000001" readonly>
                    </div>
                `;
            } else if (currentTab === 'courts') {
                formFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">名称</label>
                        <input type="text" class="form-control" name="name" value="${data.name || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="1" ${data.status === 1 ? 'selected' : ''}>启用</option>
                            <option value="0" ${data.status === 0 ? 'selected' : ''}>禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">地址</label>
                        <div class="input-group">
                            <input type="text" class="form-control" name="original_address" value="${data.original_address || ''}" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="parseAddress('courts')">解析地址</button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">省份</label>
                        <input type="text" class="form-control" name="province" value="${data.province || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">城市</label>
                        <input type="text" class="form-control" name="city" value="${data.city || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">区县</label>
                        <input type="text" class="form-control" name="district" value="${data.district || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">经度</label>
                        <input type="number" class="form-control" name="lng" value="${data.lng || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">纬度</label>
                        <input type="number" class="form-control" name="lat" value="${data.lat || ''}" step="0.0000001" readonly>
                    </div>
                `;
            } else if (currentTab === 'trials') {
                formFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">案件ID</label>
                        <input type="number" class="form-control" name="case_id" value="${data.case_id || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">法院</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="courtSearch" placeholder="搜索法院名称或ID" autocomplete="off">
                            <input type="hidden" name="court_id" value="${data.court_id || ''}">
                            <div class="dropdown-menu w-100" id="courtDropdown" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="0" ${data.status === 0 ? 'selected' : ''}>待登记</option>
                            <option value="1" ${data.status === 1 ? 'selected' : ''}>待排庭</option>
                            <option value="2" ${data.status === 2 ? 'selected' : ''}>待滴滴</option>
                            <option value="3" ${data.status === 3 ? 'selected' : ''}>待支付</option>
                            <option value="4" ${data.status === 4 ? 'selected' : ''}>待开庭</option>
                            <option value="5" ${data.status === 5 ? 'selected' : ''}>开庭缺席</option>
                            <option value="6" ${data.status === 6 ? 'selected' : ''}>已开庭</option>
                            <option value="7" ${data.status === 7 ? 'selected' : ''}>已取消</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">开庭方式</label>
                        <select class="form-select" name="mode">
                            <option value="0" ${data.mode === 0 ? 'selected' : ''}>待定</option>
                            <option value="1" ${data.mode === 1 ? 'selected' : ''}>线上</option>
                            <option value="2" ${data.mode === 2 ? 'selected' : ''}>线下</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">庭审类型</label>
                        <select class="form-select" name="type">
                            <option value="1" ${data.type === 1 ? 'selected' : ''}>民初庭</option>
                            <option value="2" ${data.type === 2 ? 'selected' : ''}>调解庭</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" name="start_time" value="${formatDateTimeForInput(data.start_time)}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" name="end_time" value="${formatDateTimeForInput(data.end_time)}" required>
                    </div>
                `;
            } else if (currentTab === 'routes') {
                formFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">类型</label>
                        <select class="form-select" name="type">
                            <option value="lawyer_to_court" ${data.type === 'lawyer_to_court' ? 'selected' : ''}>律师到法院</option>
                            <option value="court_to_court" ${data.type === 'court_to_court' ? 'selected' : ''}>法院到法院</option>
                            <option value="court_to_lawyer" ${data.type === 'court_to_lawyer' ? 'selected' : ''}>法院到律师</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">出发地</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="fromLocationSearch" placeholder="选择出发地" autocomplete="off" readonly>
                            <div class="dropdown-menu w-100" id="fromLocationDropdown" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">出发地纬度</label>
                        <input type="number" class="form-control" name="from_lat" value="${data.from_lat || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">出发地经度</label>
                        <input type="number" class="form-control" name="from_lng" value="${data.from_lng || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">出发地地址</label>
                        <input type="text" class="form-control" name="from_formatted_address" value="${data.from_formatted_address || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目的地</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="toLocationSearch" placeholder="选择目的地" autocomplete="off" readonly>
                            <div class="dropdown-menu w-100" id="toLocationDropdown" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目的地纬度</label>
                        <input type="number" class="form-control" name="to_lat" value="${data.to_lat || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目的地经度</label>
                        <input type="number" class="form-control" name="to_lng" value="${data.to_lng || ''}" step="0.0000001" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目的地地址</label>
                        <input type="text" class="form-control" name="to_formatted_address" value="${data.to_formatted_address || ''}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">距离(米)</label>
                        <input type="number" class="form-control" name="distance" value="${data.distance || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">时间(分钟)</label>
                        <input type="number" class="form-control" name="duration" value="${data.duration || ''}" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">成本</label>
                        <input type="number" class="form-control" name="cost" value="${data.cost || ''}" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="0" ${data.status === 0 ? 'selected' : ''}>禁用</option>
                            <option value="1" ${data.status === 1 ? 'selected' : ''}>可用</option>
                            <option value="2" ${data.status === 2 ? 'selected' : ''}>人工修正</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" name="remark" rows="3">${data.remark || ''}</textarea>
                    </div>
                `;
            }
        }

        function saveData() {
            const form = document.getElementById('editForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData) {
                data[key] = value;
            }

            // 转换数据类型
            if (currentTab === 'lawyers') {
                // 将字符串转换为数字
                if (data.status) data.status = parseInt(data.status);
                if (data.lng) data.lng = parseFloat(data.lng);
                if (data.lat) data.lat = parseFloat(data.lat);
                if (data.reliability) data.reliability = parseInt(data.reliability);
            } else if (currentTab === 'courts') {
                // 将字符串转换为数字
                if (data.status) data.status = parseInt(data.status);
                if (data.lng) data.lng = parseFloat(data.lng);
                if (data.lat) data.lat = parseFloat(data.lat);
                if (data.reliability) data.reliability = parseInt(data.reliability);
            } else if (currentTab === 'trials') {
                // 将字符串转换为数字
                if (data.case_id) data.case_id = parseInt(data.case_id);
                if (data.court_id) data.court_id = parseInt(data.court_id);
                if (data.status) data.status = parseInt(data.status);
                if (data.mode) data.mode = parseInt(data.mode);
                if (data.type) data.type = parseInt(data.type);
            } else if (currentTab === 'routes') {
                // 将字符串转换为数字
                if (data.from_lat) data.from_lat = parseFloat(data.from_lat);
                if (data.from_lng) data.from_lng = parseFloat(data.from_lng);
                if (data.to_lat) data.to_lat = parseFloat(data.to_lat);
                if (data.to_lng) data.to_lng = parseFloat(data.to_lng);
                if (data.distance) data.distance = parseInt(data.distance);
                if (data.duration) data.duration = parseInt(data.duration);
                if (data.cost) data.cost = parseFloat(data.cost);
                if (data.status) data.status = parseInt(data.status);
            }

            // 检查是否为编辑模式
            const editId = document.getElementById('editModal').getAttribute('data-edit-id');
            const isEdit = editId && editId !== '';
            
            const url = isEdit ? `/api/${currentTab}/${editId}` : `/api/${currentTab}`;
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('保存结果:', result);
                if (result.id || result.message || (isEdit && result.name)) {
                    alert(isEdit ? '更新成功' : '添加成功');
                    loadData();
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                } else {
                    console.error('返回数据格式异常:', result);
                    alert(isEdit ? '更新失败' : '添加失败');
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('保存失败: ' + error.message);
            });
        }

        function editItem(id) {
            openEditModal(id);
        }

        function deleteItem(id) {
            if (confirm('确定要删除这条记录吗？')) {
                fetch(`/api/${currentTab}/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(data.message);
                        loadData();
                    } else {
                        alert('删除失败');
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败');
                });
            }
        }

        // 监听行选择变化
        document.addEventListener('change', function(e) {
            if (e.target.matches('.row-select')) {
                updateSelectedIds();
            }
        });

        // 显示批量编辑模态框
        function showBatchEditModal() {
            document.getElementById('batchEditCount').textContent = selectedIds.length;
            generateBatchEditForm();
            new bootstrap.Modal(document.getElementById('batchEditModal')).show();
        }

        // 生成批量编辑表单
        function generateBatchEditForm() {
            const formFields = document.getElementById('batchEditFormFields');
            formFields.innerHTML = '';

            if (currentTab === 'lawyers') {
                formFields.innerHTML = `
                    <div class="alert alert-info">
                        <small>只有填写的字段会被更新，留空的字段保持原值不变</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">雇佣关系</label>
                        <input type="text" class="form-control" name="employment" placeholder="留空则不更新">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">职能</label>
                        <input type="text" class="form-control" name="ability" placeholder="留空则不更新">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">不更新</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                `;
            } else if (currentTab === 'courts') {
                formFields.innerHTML = `
                    <div class="alert alert-info">
                        <small>只有填写的字段会被更新，留空的字段保持原值不变</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">不更新</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                `;
            } else if (currentTab === 'trials') {
                formFields.innerHTML = `
                    <div class="alert alert-info">
                        <small>只有填写的字段会被更新，留空的字段保持原值不变</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">不更新</option>
                            <option value="0">待登记</option>
                            <option value="1">待排庭</option>
                            <option value="2">待滴滴</option>
                            <option value="3">待支付</option>
                            <option value="4">待开庭</option>
                            <option value="5">开庭缺席</option>
                            <option value="6">已开庭</option>
                            <option value="7">已取消</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">开庭方式</label>
                        <select class="form-select" name="mode">
                            <option value="">不更新</option>
                            <option value="0">待定</option>
                            <option value="1">线上</option>
                            <option value="2">线下</option>
                        </select>
                    </div>
                `;
            } else if (currentTab === 'routes') {
                formFields.innerHTML = `
                    <div class="alert alert-info">
                        <small>只有填写的字段会被更新，留空的字段保持原值不变</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status">
                            <option value="">不更新</option>
                            <option value="0">禁用</option>
                            <option value="1">可用</option>
                            <option value="2">人工修正</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">成本</label>
                        <input type="number" class="form-control" name="cost" placeholder="留空则不更新" step="0.01">
                    </div>
                `;
            }
        }

                 // 执行批量编辑
         function performBatchEdit() {
             const form = document.getElementById('batchEditForm');
             const formData = new FormData(form);
             const data = {};
             
             // 只包含非空值
             for (let [key, value] of formData) {
                 if (value.trim() !== '') {
                     data[key] = value;
                 }
             }

             if (Object.keys(data).length === 0) {
                 alert('请至少填写一个要更新的字段');
                 return;
             }

             // 为每个选中的项目执行更新
             let updatePromises = selectedIds.map(id => {
                 return fetch(`/api/${currentTab}/${id}`, {
                     method: 'PUT',
                     headers: {
                         'Content-Type': 'application/json'
                     },
                     body: JSON.stringify(data)
                 });
             });

             Promise.all(updatePromises)
                 .then(responses => {
                     const allSuccessful = responses.every(response => response.ok);
                     if (allSuccessful) {
                         alert('批量编辑成功');
                         loadData();
                         selectedIds = [];
                         bootstrap.Modal.getInstance(document.getElementById('batchEditModal')).hide();
                     } else {
                         alert('部分更新失败');
                     }
                 })
                 .catch(error => {
                     console.error('批量编辑失败:', error);
                     alert('批量编辑失败');
                 });
         }

         // 地址解析功能
         function parseAddress(type) {
             const addressInput = document.querySelector('input[name="original_address"]');
             if (!addressInput) {
                 alert('找不到地址输入框');
                 return;
             }
             
             const address = addressInput.value.trim();
             if (!address) {
                 alert('请先输入地址');
                 return;
             }

             // 显示加载状态
             const parseButtons = document.querySelectorAll('button[onclick*="parseAddress"]');
             let parseButton = null;
             for (let btn of parseButtons) {
                 if (btn.onclick && btn.onclick.toString().includes(type)) {
                     parseButton = btn;
                     break;
                 }
             }
             
             let originalText = '解析地址';
             if (parseButton) {
                 originalText = parseButton.textContent;
                 parseButton.textContent = '解析中...';
                 parseButton.disabled = true;
             }

             fetch('/api/geocoder', {
                 method: 'POST',
                 headers: {
                     'Content-Type': 'application/json'
                 },
                 body: JSON.stringify({ address: address })
             })
             .then(response => {
                 if (!response.ok) {
                     throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                 }
                 return response.json();
             })
             .then(data => {
                 console.log('地址解析成功:', data);
                 
                 // 检查返回的数据
                 if (data.error) {
                     alert('地址解析失败: ' + data.error);
                     return;
                 }
                 
                 // 填充解析结果
                 const provinceInput = document.querySelector('input[name="province"]');
                 const cityInput = document.querySelector('input[name="city"]');
                 const districtInput = document.querySelector('input[name="district"]');
                 const lngInput = document.querySelector('input[name="lng"]');
                 const latInput = document.querySelector('input[name="lat"]');
                 
                 if (provinceInput) provinceInput.value = data.province || '';
                 if (cityInput) cityInput.value = data.city || '';
                 if (districtInput) districtInput.value = data.district || '';
                 
                 if (type === 'lawyers' || type === 'courts') {
                     if (lngInput) lngInput.value = data.lng || '';
                     if (latInput) latInput.value = data.lat || '';
                 }
                 
                 alert('地址解析成功！');
             })
             .catch(error => {
                 console.error('地址解析失败:', error);
                 alert('地址解析失败: ' + error.message);
             })
             .finally(() => {
                 // 恢复按钮状态
                 if (parseButton) {
                     parseButton.textContent = originalText;
                     parseButton.disabled = false;
                 }
             });
         }

         // 法院搜索功能
         function setupCourtSearch() {
             const searchInput = document.getElementById('courtSearch');
             const dropdown = document.getElementById('courtDropdown');
             const hiddenInput = document.querySelector('input[name="court_id"]');
             
             if (!searchInput) return;

             searchInput.addEventListener('input', function() {
                 const keyword = this.value.trim();
                 
                 if (keyword.length < 1) {
                     dropdown.classList.remove('show');
                     return;
                 }

                 fetch(`/api/courts/search?keyword=${encodeURIComponent(keyword)}`)
                     .then(response => response.json())
                     .then(courts => {
                         dropdown.innerHTML = '';
                         
                         if (courts.length === 0) {
                             dropdown.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的法院</div>';
                         } else {
                             courts.forEach(court => {
                                 const item = document.createElement('div');
                                 item.className = 'dropdown-item';
                                 item.innerHTML = `<strong>${court.name}</strong> (ID: ${court.id})<br><small class="text-muted">${court.original_address}</small>`;
                                 item.addEventListener('click', function() {
                                     searchInput.value = `${court.name} (ID: ${court.id})`;
                                     hiddenInput.value = court.id;
                                     dropdown.classList.remove('show');
                                 });
                                 dropdown.appendChild(item);
                             });
                         }
                         
                         dropdown.classList.add('show');
                     })
                     .catch(error => {
                         console.error('搜索法院失败:', error);
                     });
             });

             // 点击外部关闭下拉框
             document.addEventListener('click', function(e) {
                 if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                     dropdown.classList.remove('show');
                 }
             });
         }

         // 路线位置选择功能
         function setupRouteLocationSearch() {
             const typeSelect = document.querySelector('select[name="type"]');
             const fromSearch = document.getElementById('fromLocationSearch');
             const toSearch = document.getElementById('toLocationSearch');
             
             if (!typeSelect || !fromSearch || !toSearch) return;

             // 监听类型变化
             typeSelect.addEventListener('change', function() {
                 const routeType = this.value;
                 
                 // 重置选择
                 fromSearch.value = '';
                 toSearch.value = '';
                 clearLocationInputs();
                 
                 // 根据类型启用相应的搜索
                 if (routeType === 'lawyer_to_court') {
                     fromSearch.placeholder = '搜索律师';
                     toSearch.placeholder = '搜索法院';
                     fromSearch.removeAttribute('readonly');
                     toSearch.removeAttribute('readonly');
                     setupLocationSearch(fromSearch, 'lawyers', 'from');
                     setupLocationSearch(toSearch, 'courts', 'to');
                 } else if (routeType === 'court_to_court') {
                     fromSearch.placeholder = '搜索出发法院';
                     toSearch.placeholder = '搜索目标法院';
                     fromSearch.removeAttribute('readonly');
                     toSearch.removeAttribute('readonly');
                     setupLocationSearch(fromSearch, 'courts', 'from');
                     setupLocationSearch(toSearch, 'courts', 'to');
                 } else if (routeType === 'court_to_lawyer') {
                     fromSearch.placeholder = '搜索法院';
                     toSearch.placeholder = '搜索律师';
                     fromSearch.removeAttribute('readonly');
                     toSearch.removeAttribute('readonly');
                     setupLocationSearch(fromSearch, 'courts', 'from');
                     setupLocationSearch(toSearch, 'lawyers', 'to');
                 }
             });
         }

         function setupLocationSearch(input, searchType, direction) {
             const dropdownId = direction + 'LocationDropdown';
             const dropdown = document.getElementById(dropdownId);
             
             input.addEventListener('input', function() {
                 const keyword = this.value.trim();
                 
                 if (keyword.length < 1) {
                     dropdown.classList.remove('show');
                     return;
                 }

                 const endpoint = searchType === 'lawyers' ? '/api/lawyers/search' : '/api/courts/search';
                 
                 fetch(`${endpoint}?keyword=${encodeURIComponent(keyword)}`)
                     .then(response => response.json())
                     .then(items => {
                         dropdown.innerHTML = '';
                         
                         if (items.length === 0) {
                             dropdown.innerHTML = `<div class="dropdown-item text-muted">未找到匹配的${searchType === 'lawyers' ? '律师' : '法院'}</div>`;
                         } else {
                             items.forEach(item => {
                                 const itemElement = document.createElement('div');
                                 itemElement.className = 'dropdown-item';
                                 itemElement.innerHTML = `<strong>${item.name}</strong> (ID: ${item.id})<br><small class="text-muted">${item.original_address}</small>`;
                                 itemElement.addEventListener('click', function() {
                                     input.value = `${item.name} (ID: ${item.id})`;
                                     fillLocationInputs(item, direction);
                                     dropdown.classList.remove('show');
                                 });
                                 dropdown.appendChild(itemElement);
                             });
                         }
                         
                         dropdown.classList.add('show');
                     })
                     .catch(error => {
                         console.error('搜索失败:', error);
                     });
             });
         }

         function fillLocationInputs(item, direction) {
             const prefix = direction === 'from' ? 'from_' : 'to_';
             document.querySelector(`input[name="${prefix}lat"]`).value = item.lat || '';
             document.querySelector(`input[name="${prefix}lng"]`).value = item.lng || '';
             document.querySelector(`input[name="${prefix}formatted_address"]`).value = item.formatted_address || item.original_address || '';
         }

         function clearLocationInputs() {
             ['from_lat', 'from_lng', 'from_formatted_address', 'to_lat', 'to_lng', 'to_formatted_address'].forEach(name => {
                 const input = document.querySelector(`input[name="${name}"]`);
                 if (input) input.value = '';
             });
         }

         // 遗传算法调度相关函数
         let currentGeneticSolutions = [];
         let currentGeneticConfig = null;

         // 设置智能调度tab
         function setupGeneticTab() {
             loadGeneticConfigs();
             setupGeneticEventListeners();
             setupGeneticSearchAndButtons();
         }

         // 隐藏搜索和操作按钮
         function hideSearchAndOperationButtons() {
             const searchInput = document.getElementById('searchInput');
             if (searchInput && searchInput.parentElement && searchInput.parentElement.parentElement) {
                 searchInput.parentElement.parentElement.style.display = 'none';
             }
             const paginationContainer = document.querySelector('.pagination-container');
             if (paginationContainer) {
                 paginationContainer.style.display = 'none';
             }
         }

         // 显示搜索和操作按钮
         function showSearchAndOperationButtons() {
             const searchInput = document.getElementById('searchInput');
             if (searchInput && searchInput.parentElement && searchInput.parentElement.parentElement) {
                 const searchContainer = searchInput.parentElement.parentElement;
                 searchContainer.style.display = 'block';
             }
         }

         // 设置智能调度的搜索和按钮
         function setupGeneticSearchAndButtons() {
             // 显示搜索区域
             const searchInput = document.getElementById('searchInput');
             if (searchInput && searchInput.parentElement && searchInput.parentElement.parentElement) {
                 const searchContainer = searchInput.parentElement.parentElement;
                 searchContainer.style.display = 'block';
                 
                 // 设置搜索框placeholder
                 searchInput.placeholder = '搜索法院/地址/律师名...';
             }
             
             // 禁用状态过滤下拉框
             const statusFilter = document.getElementById('statusFilter');
             if (statusFilter) {
                 statusFilter.disabled = true;
                 statusFilter.style.background = '#f8f9fa';
                 statusFilter.style.color = '#6c757d';
             }
             
             // 隐藏操作按钮
             const addBtn = document.getElementById('addBtn');
             const batchEditBtn = document.getElementById('batchEditBtn');
             const batchDeleteBtn = document.getElementById('batchDeleteBtn');
             
             if (addBtn) addBtn.style.display = 'none';
             if (batchEditBtn) batchEditBtn.style.display = 'none';
             if (batchDeleteBtn) batchDeleteBtn.style.display = 'none';
             
             // 隐藏分页功能
             const paginationContainer = document.getElementById('paginationContainer');
             if (paginationContainer) {
                 paginationContainer.style.display = 'none';
             }
         }

         // 恢复正常的搜索和按钮功能
         function restoreNormalSearchAndButtons() {
             // 显示搜索区域
             const searchInput = document.getElementById('searchInput');
             if (searchInput && searchInput.parentElement && searchInput.parentElement.parentElement) {
                 const searchContainer = searchInput.parentElement.parentElement;
                 searchContainer.style.display = 'block';
                 
                 // 恢复搜索框placeholder
                 searchInput.placeholder = '搜索ID/名称/地址...';
             }
             
             // 启用状态过滤下拉框
             const statusFilter = document.getElementById('statusFilter');
             if (statusFilter) {
                 statusFilter.disabled = false;
                 statusFilter.style.background = '';
                 statusFilter.style.color = '';
             }
             
             // 显示操作按钮
             const addBtn = document.getElementById('addBtn');
             const batchEditBtn = document.getElementById('batchEditBtn');
             const batchDeleteBtn = document.getElementById('batchDeleteBtn');
             
             if (addBtn) addBtn.style.display = 'inline-block';
             if (batchEditBtn) batchEditBtn.style.display = 'inline-block';
             if (batchDeleteBtn) batchDeleteBtn.style.display = 'inline-block';
             
             // 显示分页功能
             const paginationContainer = document.getElementById('paginationContainer');
             if (paginationContainer) {
                 paginationContainer.style.display = 'flex';
             }
         }

         // 加载遗传算法配置
         function loadGeneticConfigs() {
             fetch('/api/genetic/configs')
                 .then(response => {
                     if (!response.ok) {
                         throw new Error(`HTTP error! status: ${response.status}`);
                     }
                     return response.json();
                 })
                 .then(data => {
                     console.log('Loaded genetic configs:', data);
                     const select = document.getElementById('geneticConfigSelect');
                     select.innerHTML = '<option value="">选择权重配置</option>';
                     
                     if (data.configs && Array.isArray(data.configs)) {
                         data.configs.forEach((config, index) => {
                             const option = document.createElement('option');
                             option.value = index;
                             option.textContent = config.name;
                             option.dataset.config = JSON.stringify(config);
                             
                             // 默认选择"时间优先"
                             if (config.name === "时间优先") {
                                 option.selected = true;
                                 // 设置权重输入框的值
                                 updateWeightInputs(config);
                             }
                             
                             select.appendChild(option);
                         });
                         
                         // 触发默认选择的配置变更事件
                         const timeOption = select.querySelector('option[selected]');
                         if (timeOption) {
                             currentGeneticConfig = JSON.parse(timeOption.dataset.config);
                             console.log('Default selected config:', currentGeneticConfig);
                         }
                     } else {
                         console.error('Invalid configs data:', data);
                         showError('配置数据格式错误');
                     }
                 })
                 .catch(error => {
                     console.error('加载配置失败:', error);
                     showError('加载配置失败: ' + error.message);
                     
                     // 如果API失败，使用默认配置
                     const defaultConfigs = [
                         {name: "距离优先", dist_weight: 10.0, cost_weight: 1.0, time_weight: 1.0, workload_weight: 0.1},
                         {name: "时间优先", dist_weight: 1.0, cost_weight: 1.0, time_weight: 10.0, workload_weight: 0.1},
                         {name: "负载均衡", dist_weight: 2.0, cost_weight: 2.0, time_weight: 2.0, workload_weight: 10.0},
                         {name: "均衡优化", dist_weight: 5.0, cost_weight: 5.0, time_weight: 5.0, workload_weight: 1.0},
                         {name: "综合平衡", dist_weight: 6.0, cost_weight: 6.0, time_weight: 6.0, workload_weight: 3.0}
                     ];
                     
                     const select = document.getElementById('geneticConfigSelect');
                     select.innerHTML = '<option value="">选择权重配置</option>';
                     
                     defaultConfigs.forEach((config, index) => {
                         const option = document.createElement('option');
                         option.value = index;
                         option.textContent = config.name;
                         option.dataset.config = JSON.stringify(config);
                         
                         // 默认选择"时间优先"
                         if (config.name === "时间优先") {
                             option.selected = true;
                             // 设置权重输入框的值
                             updateWeightInputs(config);
                         }
                         
                         select.appendChild(option);
                     });
                     
                     // 触发默认选择的配置变更事件
                     const timeOption = select.querySelector('option[selected]');
                     if (timeOption) {
                         currentGeneticConfig = JSON.parse(timeOption.dataset.config);
                         console.log('Default selected config:', currentGeneticConfig);
                     }
                 });
         }

         // 更新权重输入框的值
         function updateWeightInputs(config) {
             document.getElementById('distanceWeight').value = config.dist_weight || 1.0;
             document.getElementById('costWeight').value = config.cost_weight || 1.0;
             document.getElementById('timeWeight').value = config.time_weight || 1.0;
             document.getElementById('workloadWeight').value = config.workload_weight || 1.0;
         }

         // 从输入框获取权重配置
         function getWeightConfigFromInputs() {
             return {
                 name: "自定义配置",
                 dist_weight: parseFloat(document.getElementById('distanceWeight').value) || 1.0,
                 cost_weight: parseFloat(document.getElementById('costWeight').value) || 1.0,
                 time_weight: parseFloat(document.getElementById('timeWeight').value) || 1.0,
                 workload_weight: parseFloat(document.getElementById('workloadWeight').value) || 1.0
             };
         }

         // 设置遗传算法事件监听器
         function setupGeneticEventListeners() {
             // 配置选择变化
             document.getElementById('geneticConfigSelect').addEventListener('change', function() {
                 const selectedIndex = this.value;
                 if (selectedIndex !== '' && this.options[this.selectedIndex]) {
                     const option = this.options[this.selectedIndex];
                     if (option.dataset && option.dataset.config) {
                         try {
                             const config = JSON.parse(option.dataset.config);
                             currentGeneticConfig = config;
                             
                             // 更新权重输入框
                             updateWeightInputs(config);
                             
                             // 查找该配置的解决方案
                             const solution = currentGeneticSolutions.find(s => s.config_name === config.name);
                             if (solution) {
                                 displayGeneticSolution(solution);
                             } else {
                                 clearGeneticDisplay();
                             }
                         } catch (e) {
                             console.error('解析配置失败:', e);
                             clearGeneticDisplay();
                         }
                     } else {
                         clearGeneticDisplay();
                     }
                 } else {
                     clearGeneticDisplay();
                 }
             });

             // 方案选择变化
             document.getElementById('solutionSelect').addEventListener('change', function() {
                 const selectedIndex = this.value;
                 if (selectedIndex !== '' && currentGeneticSolutions[selectedIndex]) {
                     const solution = currentGeneticSolutions[selectedIndex];
                     displayGeneticSolution(solution);
                 }
             });

             // 重新计算按钮
             document.getElementById('runGeneticBtn').addEventListener('click', function() {
                 runGeneticScheduling();
             });

             // 地图显示按钮
             document.getElementById('showMapBtn').addEventListener('click', function() {
                 showGeneticMap();
             });
         }

         // 运行遗传算法调度
         function runGeneticScheduling() {
             const button = document.getElementById('runGeneticBtn');
             button.disabled = true;
             button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 计算中...';

             // 使用输入框的权重值
             const customConfig = getWeightConfigFromInputs();
             const requestData = {
                 configs: [customConfig]
             };

             console.log('Sending request:', requestData);

             fetch('/api/genetic/schedule', {
                 method: 'POST',
                 headers: {
                     'Content-Type': 'application/json'
                 },
                 body: JSON.stringify(requestData)
             })
             .then(response => {
                 if (!response.ok) {
                     throw new Error(`HTTP error! status: ${response.status}`);
                 }
                 return response.json();
             })
             .then(data => {
                 console.log('Received response:', data);
                 if (data.solutions && data.solutions.length > 0) {
                     currentGeneticSolutions = data.solutions;
                     
                     // 填充方案下拉框
                     const solutionSelect = document.getElementById('solutionSelect');
                     solutionSelect.innerHTML = '<option value="">选择方案</option>';
                     
                     // 取前10个方案
                     const top10Solutions = data.solutions.slice(0, 10);
                     top10Solutions.forEach((solution, index) => {
                         const option = document.createElement('option');
                         option.value = index;
                         option.textContent = `方案 ${index + 1}: 匹配 ${solution.match_count} 开庭(¥${solution.total_cost.toFixed(2)} | ${(solution.total_distance/1000).toFixed(2)}KM | ${Math.round(solution.total_duration)}MIN)`;
                         solutionSelect.appendChild(option);
                     });
                     
                     // 默认选择第一个方案
                     if (top10Solutions.length > 0) {
                         solutionSelect.value = 0;
                         displayGeneticSolution(top10Solutions[0]);
                     }
                     
                     showSuccess('遗传算法调度计算完成！');
                 } else {
                     showError('未找到可行的调度方案');
                     clearGeneticDisplay();
                 }
             })
             .catch(error => {
                 console.error('调度计算失败:', error);
                 showError('调度计算失败: ' + error.message);
                 clearGeneticDisplay();
             })
             .finally(() => {
                 button.disabled = false;
                 button.innerHTML = '<i class="fas fa-play"></i> 重新计算';
             });
         }

         // 显示遗传算法解决方案
         function displayGeneticSolution(solution) {
             console.log('Displaying genetic solution:', solution);
             
             // 显示第二行和第三部分
             document.getElementById('solutionRow').style.display = 'flex';
             document.getElementById('scheduleTableContainer').style.display = 'block';

             // 生成时间表格
             generateScheduleTable(solution);
             
             // 设置滚动同步
             setupScrollSync();
         }

         // 生成时间表格
         function generateScheduleTable(solution) {
             console.log('Generating schedule table for solution:', solution);
             
             const tableHead = document.getElementById('scheduleTableHead');
             const tableBody = document.getElementById('scheduleTableBody');
             const fixedTableHead = document.getElementById('scheduleTableHeadFixed');
             const fixedTableBody = document.getElementById('scheduleTableBodyFixed');
             
             // 确保 solution 和 lawyer_schedules 都存在且是数组
             if (!solution || !solution.lawyer_schedules || !Array.isArray(solution.lawyer_schedules) || solution.lawyer_schedules.length === 0) {
                 console.log('No lawyer schedules found or invalid data structure');
                 tableHead.innerHTML = '';
                 tableBody.innerHTML = '';
                 fixedTableHead.innerHTML = '';
                 fixedTableBody.innerHTML = `
                     <tr>
                         <td class="text-center schedule-empty">
                             暂无律师分配方案
                         </td>
                     </tr>
                 `;
                 return;
             }
             
             // 收集所有日期
             const allDates = new Set();
             solution.lawyer_schedules.forEach(schedule => {
                 if (schedule.trial_details && Array.isArray(schedule.trial_details)) {
                     schedule.trial_details.forEach(trial => {
                         if (trial.start_time) {
                             const date = trial.start_time.split('T')[0];
                             allDates.add(date);
                         }
                     });
                 }
             });
             
             // 过滤出从明天开始的日期并排序
             const tomorrow = new Date();
             tomorrow.setDate(tomorrow.getDate() + 1);
             tomorrow.setHours(0, 0, 0, 0);
             
             console.log('All dates found:', Array.from(allDates));
             console.log('Tomorrow:', tomorrow);
             
             const validDates = Array.from(allDates)
                 .filter(dateStr => {
                     const date = new Date(dateStr);
                     console.log('Checking date:', dateStr, 'parsed as:', date, 'vs tomorrow:', tomorrow, 'result:', date >= tomorrow);
                     return date >= tomorrow;
                 })
                 .sort();
             
             console.log('Valid dates after filtering:', validDates);
             
             if (validDates.length === 0) {
                 tableHead.innerHTML = '';
                 tableBody.innerHTML = '';
                 fixedTableHead.innerHTML = '';
                 fixedTableBody.innerHTML = `
                     <tr>
                         <td class="text-center schedule-empty">
                             暂无从明天开始的开庭安排
                         </td>
                     </tr>
                 `;
                 return;
             }
             
             // 生成固定列表头
             const fixedHeaderRow = document.createElement('tr');
             const lawyerHeader = document.createElement('th');
             lawyerHeader.textContent = '律师(开庭数)';
             fixedHeaderRow.appendChild(lawyerHeader);
             fixedTableHead.innerHTML = '';
             fixedTableHead.appendChild(fixedHeaderRow);
             
             // 生成滚动列表头
             const scrollHeaderRow = document.createElement('tr');
             validDates.forEach(date => {
                 const th = document.createElement('th');
                 const dateObj = new Date(date);
                 const today = new Date();
                 today.setHours(0, 0, 0, 0);
                 const tomorrow = new Date(today);
                 tomorrow.setDate(tomorrow.getDate() + 1);
                 
                 let displayText;
                 if (dateObj.getTime() === today.getTime()) {
                     displayText = '今天';
                 } else if (dateObj.getTime() === tomorrow.getTime()) {
                     displayText = '明天';
                 } else {
                     displayText = `${dateObj.getMonth() + 1}/${dateObj.getDate()}`;
                 }
                 
                 th.textContent = displayText;
                 scrollHeaderRow.appendChild(th);
             });
             
             tableHead.innerHTML = '';
             tableHead.appendChild(scrollHeaderRow);
             
             // 生成表格内容
             tableBody.innerHTML = '';
             fixedTableBody.innerHTML = '';
             
             solution.lawyer_schedules.forEach(schedule => {
                 if (!schedule || !schedule.lawyer_name) {
                     console.warn('Invalid schedule data:', schedule);
                     return;
                 }
                 
                 const scrollRow = document.createElement('tr');
                 const fixedRow = document.createElement('tr');
                 
                 // 律师姓名和开庭数 - 只计算从明天开始的开庭
                 const nameCell = document.createElement('td');
                 nameCell.className = 'lawyer-name';
                 
                 // 计算从明天开始的开庭数
                 const futureTrials = (schedule.trial_details && Array.isArray(schedule.trial_details)) ? 
                     schedule.trial_details.filter(trial => {
                         if (!trial.start_time) return false;
                         const trialDate = new Date(trial.start_time.split('T')[0]);
                         return validDates.some(dateStr => {
                             const validDate = new Date(dateStr);
                             return trialDate.getTime() === validDate.getTime();
                         });
                     }) : [];
                 
                 const trialCount = futureTrials.length;
                 
                 nameCell.innerHTML = `
                     <div class="lawyer-info clickable" data-schedule-index="${solution.lawyer_schedules.indexOf(schedule)}">
                         <span class="name">${schedule.lawyer_name}</span>
                         <span class="trial-count">(${trialCount})</span>
                         <small class="text-muted d-block">${schedule.lawyer_address || '地址信息缺失'}</small>
                     </div>
                 `;
                 
                 // 添加点击事件监听器
                 nameCell.querySelector('.lawyer-info').addEventListener('click', function() {
                     showLawyerDetail(schedule);
                 });
                 
                 fixedRow.appendChild(nameCell);
                 
                 // 为每个有效日期创建单元格
                 validDates.forEach(date => {
                     const cell = document.createElement('td');
                     cell.className = 'schedule-cell';
                     
                     // 找到这个日期的所有开庭
                     const dayTrials = (schedule.trial_details && Array.isArray(schedule.trial_details)) ? 
                         schedule.trial_details.filter(trial => 
                             trial.start_time && trial.start_time.split('T')[0] === date
                         ) : [];
                     
                     if (dayTrials.length > 0) {
                         // 按时间排序
                         dayTrials.sort((a, b) => {
                             const timeA = new Date(a.start_time).getTime();
                             const timeB = new Date(b.start_time).getTime();
                             return timeA - timeB;
                         });
                         
                         const trialStack = document.createElement('div');
                         trialStack.className = 'trial-stack';
                         
                         dayTrials.forEach(trial => {
                             if (!trial.start_time || !trial.end_time) {
                                 console.warn('Invalid trial data:', trial);
                                 return;
                             }
                             
                             const startTime = new Date(trial.start_time);
                             const endTime = new Date(trial.end_time);
                             
                             // 使用后端计算的实际去程返程时间
                             const travelStart = trial.travel_start ? new Date(trial.travel_start) : new Date(startTime.getTime() - (trial.duration || 0) * 60 * 1000 / 2);
                             const travelEnd = trial.travel_end ? new Date(trial.travel_end) : new Date(endTime.getTime() + (trial.duration || 0) * 60 * 1000 / 2);
                             
                             // 获取距离信息
                             const distanceKm = trial.distance ? (trial.distance / 1000).toFixed(1) : '0.0';
                             
                             // 创建去程层
                             const travelGoLayer = document.createElement('div');
                             travelGoLayer.className = 'trial-layer travel-go';
                             travelGoLayer.innerHTML = `
                                 <span class="trial-time">${formatTime(travelStart)} ~ ${formatTime(startTime)}</span>
                                 <small class="text-muted">去程 ${distanceKm}KM</small>
                             `;
                             
                             // 创建开庭层
                             const courtLayer = document.createElement('div');
                             courtLayer.className = 'trial-layer court-session clickable';
                             courtLayer.onclick = () => showCourtDetail(trial);
                             courtLayer.innerHTML = `
                                 <span class="trial-court">${trial.court_name || '未知法院'}</span>
                                 <span class="trial-time">${formatTime(startTime)} ~ ${formatTime(endTime)}</span>
                             `;
                             
                             // 创建返程层
                             const travelBackLayer = document.createElement('div');
                             travelBackLayer.className = 'trial-layer travel-back';
                             travelBackLayer.innerHTML = `
                                 <span class="trial-time">${formatTime(endTime)} ~ ${formatTime(travelEnd)}</span>
                                 <small class="text-muted">返程 ${distanceKm}KM</small>
                             `;
                             
                             trialStack.appendChild(travelGoLayer);
                             trialStack.appendChild(courtLayer);
                             trialStack.appendChild(travelBackLayer);
                         });
                         
                         cell.appendChild(trialStack);
                     }
                     
                     scrollRow.appendChild(cell);
                 });
                 
                 tableBody.appendChild(scrollRow);
                 fixedTableBody.appendChild(fixedRow);
             });
         }
         
         // 格式化时间
         function formatTime(date) {
             return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
         }

         // 设置滚动同步
         function setupScrollSync() {
             const fixedTable = document.querySelector('.fixed-column');
             const scrollableTable = document.querySelector('.scrollable-columns');
             
             if (fixedTable && scrollableTable) {
                 // 监听滚动列的垂直滚动，同步到固定列
                 scrollableTable.addEventListener('scroll', function() {
                     fixedTable.scrollTop = this.scrollTop;
                 });
                 
                 // 监听固定列的垂直滚动，同步到滚动列
                 fixedTable.addEventListener('scroll', function() {
                     scrollableTable.scrollTop = this.scrollTop;
                 });
             }
         }

         // 显示律师详情模态框
         function showLawyerDetail(schedule) {
             const modal = new bootstrap.Modal(document.getElementById('detailModal'));
             const modalTitle = document.getElementById('detailModalLabel');
             const modalBody = document.getElementById('detailModalBody');
             
             modalTitle.textContent = `律师详情 - ${schedule.lawyer_name}`;
             
             // 计算统计信息
             const totalTrials = schedule.trial_details ? schedule.trial_details.length : 0;
             const totalDistance = schedule.trial_details ? schedule.trial_details.reduce((sum, trial) => sum + (trial.distance || 0), 0) : 0;
             const totalDuration = schedule.trial_details ? schedule.trial_details.reduce((sum, trial) => sum + (trial.duration || 0), 0) : 0;
             
             let modalContent = `
                 <div class="row">
                     <div class="col-md-12">
                         <h6><i class="fas fa-user-tie"></i> 基本信息</h6>
                         <table class="table table-sm">
                             <tr><th width="120">律师姓名:</th><td><strong>${schedule.lawyer_name}</strong></td></tr>
                             <tr><th>律师ID:</th><td>${schedule.lawyer_id}</td></tr>
                             <tr><th>律师地址:</th><td><span class="text-muted">${schedule.lawyer_address || '地址信息缺失'}</span></td></tr>
                             <tr><th>开庭数量:</th><td><span class="badge bg-primary">${totalTrials} 场</span></td></tr>
                             <tr><th>总行程:</th><td><span class="badge bg-success">${(totalDistance / 1000).toFixed(2)} 公里</span></td></tr>
                             <tr><th>总耗时:</th><td><span class="badge bg-warning">${Math.round(totalDuration)} 分钟</span></td></tr>
                         </table>
                     </div>
                 </div>
             `;
             
             if (schedule.trial_details && schedule.trial_details.length > 0) {
                 modalContent += `
                     <div class="row mt-3">
                         <div class="col-md-12">
                             <h6><i class="fas fa-calendar-alt"></i> 开庭安排</h6>
                             <div class="table-responsive">
                                 <table class="table table-striped table-sm">
                                     <thead>
                                         <tr>
                                             <th>日期</th>
                                             <th>时间</th>
                                             <th>法院</th>
                                             <th>地址</th>
                                             <th>出行时间</th>
                                             <th>行程</th>
                                         </tr>
                                     </thead>
                                     <tbody>
                 `;
                 
                 schedule.trial_details.forEach(trial => {
                     const startDate = new Date(trial.start_time);
                     const endDate = new Date(trial.end_time);
                     const dateStr = `${startDate.getMonth() + 1}/${startDate.getDate()}`;
                     const timeStr = `${formatTime(startDate)} - ${formatTime(endDate)}`;
                     const travelStart = trial.travel_start ? new Date(trial.travel_start) : new Date(startDate.getTime() - (trial.duration || 0) * 60 * 1000 / 2);
                     const travelEnd = trial.travel_end ? new Date(trial.travel_end) : new Date(endDate.getTime() + (trial.duration || 0) * 60 * 1000 / 2);
                     const travelTimeStr = `${formatTime(travelStart)} - ${formatTime(travelEnd)}`;
                     
                     modalContent += `
                         <tr>
                             <td><span class="badge bg-info">${dateStr}</span></td>
                             <td><span class="badge bg-success">${timeStr}</span></td>
                             <td><strong>${trial.court_name || '未知法院'}</strong></td>
                             <td><small class="text-muted">${trial.court_address || '未知地址'}</small></td>
                             <td><span class="badge bg-warning">${travelTimeStr}</span></td>
                             <td><span class="badge bg-danger">${((trial.distance || 0) / 1000).toFixed(1)}km</span></td>
                         </tr>
                     `;
                 });
                 
                 modalContent += `
                                     </tbody>
                                 </table>
                             </div>
                         </div>
                     </div>
                 `;
             }
             
             modalBody.innerHTML = modalContent;
             modal.show();
         }

         // 显示法院详情模态框
         function showCourtDetail(trial) {
             const modal = new bootstrap.Modal(document.getElementById('detailModal'));
             const modalTitle = document.getElementById('detailModalLabel');
             const modalBody = document.getElementById('detailModalBody');
             
             modalTitle.textContent = `法院详情 - ${trial.court_name}`;
             
             const startDate = new Date(trial.start_time);
             const endDate = new Date(trial.end_time);
             const dateStr = `${startDate.getFullYear()}年${startDate.getMonth() + 1}月${startDate.getDate()}日`;
             const timeStr = `${formatTime(startDate)} - ${formatTime(endDate)}`;
             
             // 计算出行时间
             const travelStart = trial.travel_start ? new Date(trial.travel_start) : new Date(startDate.getTime() - (trial.duration || 0) * 60 * 1000 / 2);
             const travelEnd = trial.travel_end ? new Date(trial.travel_end) : new Date(endDate.getTime() + (trial.duration || 0) * 60 * 1000 / 2);
             const travelTimeStr = `${formatTime(travelStart)} - ${formatTime(travelEnd)}`;
             
             const modalContent = `
                 <div class="row">
                     <div class="col-md-12">
                         <h6><i class="fas fa-university"></i> 法院信息</h6>
                         <table class="table table-sm">
                             <tr><th width="120">法院名称:</th><td><strong>${trial.court_name || '未知法院'}</strong></td></tr>
                             <tr><th>法院ID:</th><td>${trial.court_id}</td></tr>
                             <tr><th>法院地址:</th><td>${trial.court_address || '未知地址'}</td></tr>
                         </table>
                     </div>
                 </div>
                 <div class="row mt-3">
                     <div class="col-md-12">
                         <h6><i class="fas fa-gavel"></i> 开庭信息</h6>
                         <table class="table table-sm">
                             <tr><th width="120">开庭日期:</th><td><span class="badge bg-info">${dateStr}</span></td></tr>
                             <tr><th>开庭时间:</th><td><span class="badge bg-success">${timeStr}</span></td></tr>
                             <tr><th>案件ID:</th><td>${trial.case_id}</td></tr>
                             <tr><th>审判ID:</th><td>${trial.trial_id}</td></tr>
                             <tr><th>行程距离:</th><td><span class="badge bg-warning">${((trial.distance || 0) / 1000).toFixed(1)} 公里</span></td></tr>
                             <tr><th>预计耗时:</th><td><span class="badge bg-danger">${Math.round(trial.duration || 0)} 分钟</span></td></tr>
                         </table>
                     </div>
                 </div>
                 <div class="row mt-3">
                     <div class="col-md-12">
                         <h6><i class="fas fa-route"></i> 出行安排</h6>
                         <div class="alert alert-light">
                             <div class="row">
                                 <div class="col-md-4">
                                     <div class="text-center">
                                         <i class="fas fa-arrow-right text-success"></i>
                                         <h6 class="mt-2">去程</h6>
                                         <p class="mb-0">${formatTime(travelStart)} ~ ${formatTime(startDate)}</p>
                                     </div>
                                 </div>
                                 <div class="col-md-4">
                                     <div class="text-center">
                                         <i class="fas fa-gavel text-primary"></i>
                                         <h6 class="mt-2">开庭</h6>
                                         <p class="mb-0">${timeStr}</p>
                                     </div>
                                 </div>
                                 <div class="col-md-4">
                                     <div class="text-center">
                                         <i class="fas fa-arrow-left text-warning"></i>
                                         <h6 class="mt-2">返程</h6>
                                         <p class="mb-0">${formatTime(endDate)} ~ ${formatTime(travelEnd)}</p>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             `;
             
             modalBody.innerHTML = modalContent;
             modal.show();
         }

         // 清空智能调度显示
         function clearGeneticDisplay() {
             // 隐藏第二行和第三部分
             document.getElementById('solutionRow').style.display = 'none';
             document.getElementById('scheduleTableContainer').style.display = 'none';
             
             // 清空选择框
             document.getElementById('solutionSelect').innerHTML = '<option value="">选择方案</option>';
             
             // 清空表格
             const tableHead = document.getElementById('scheduleTableHead');
             const tableBody = document.getElementById('scheduleTableBody');
             const fixedTableHead = document.getElementById('scheduleTableHeadFixed');
             const fixedTableBody = document.getElementById('scheduleTableBodyFixed');
             
             tableHead.innerHTML = '';
             tableBody.innerHTML = '';
             fixedTableHead.innerHTML = '';
             fixedTableBody.innerHTML = '';
         }

         // 显示遗传算法地图
         function showGeneticMap() {
             // 获取当前选中的方案
             const selectedSolutionIndex = document.getElementById('solutionSelect').value;
             if (!selectedSolutionIndex || selectedSolutionIndex === '') {
                 showError('请先选择方案');
                 return;
             }

             if (!currentGeneticSolutions || currentGeneticSolutions.length === 0) {
                 showError('请先运行遗传算法计算');
                 return;
             }

             const solution = currentGeneticSolutions[selectedSolutionIndex];
             if (!solution) {
                 showError('选中的方案不存在');
                 return;
             }

             // 构建地图数据
             const mapData = {
                 config: currentGeneticConfig || {},
                 solution: solution
             };

             // 将数据编码为URL参数
             const encodedData = encodeURIComponent(JSON.stringify(mapData));
             const mapUrl = `/genetic/map?data=${encodedData}`;

             // 新开窗口显示地图
             window.open(mapUrl, '_blank', 'width=1200,height=800');
         }



         // 显示成功消息
         function showSuccess(message) {
             // 创建一个简单的成功提示
             const alert = document.createElement('div');
             alert.className = 'alert alert-success alert-dismissible fade show';
             alert.style.position = 'fixed';
             alert.style.top = '20px';
             alert.style.right = '20px';
             alert.style.zIndex = '9999';
             alert.innerHTML = `
                 <i class="fas fa-check-circle me-2"></i>${message}
                 <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
             `;
             
             document.body.appendChild(alert);
             
             // 3秒后自动关闭
             setTimeout(() => {
                 if (alert.parentNode) {
                     alert.parentNode.removeChild(alert);
                 }
             }, 3000);
         }

         // 显示错误消息
         function showError(message) {
             // 创建一个简单的错误提示
             const alert = document.createElement('div');
             alert.className = 'alert alert-danger alert-dismissible fade show';
             alert.style.position = 'fixed';
             alert.style.top = '20px';
             alert.style.right = '20px';
             alert.style.zIndex = '9999';
             alert.innerHTML = `
                 <i class="fas fa-exclamation-triangle me-2"></i>${message}
                 <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
             `;
             
             document.body.appendChild(alert);
             
             // 5秒后自动关闭
             setTimeout(() => {
                 if (alert.parentNode) {
                     alert.parentNode.removeChild(alert);
                 }
             }, 5000);
         }
    </script>

    <!-- 详细信息模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">详细信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html> 